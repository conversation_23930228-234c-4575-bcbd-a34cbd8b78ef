package com.ruoyi.manager.factory;

import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.fat.domain.FatModelSet;
import com.ruoyi.fat.domain.FatPicture;
import com.ruoyi.fat.domain.FatVisitRecord;
import com.ruoyi.fat.service.IFatModelSetService;
import com.ruoyi.fat.service.IFatVisitRecordService;
import com.ruoyi.web.domain.CaszDynamicForm;
import com.ruoyi.web.domain.CaszExamResult;
import com.ruoyi.web.domain.CaszHealthIntervene;
import com.ruoyi.web.domain.CaszScreenForm;
import com.ruoyi.web.service.*;
import com.ruoyi.web.utils.AIUtils;
import com.ruoyi.web.utils.FormTextValueUtils;
import com.ruoyi.web.utils.JsonUtils;
import com.google.json.JsonSanitizer;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

/**
 * 异步工厂（产生任务用）
 *
 * <AUTHOR>
 */
public class AIAsyncFactory {
    private static final Logger im_logger = LoggerFactory.getLogger("ai");

    /**
     * 对话总结后台运行
     *
     * @return 任务task
     */
    public static TimerTask advice(String visitId, String otherRemarks) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
                    String progressKey = "advice_progress_sync:" + visitId;
                    redisCache.setCacheObject(progressKey, "准备分析中", 300, java.util.concurrent.TimeUnit.MINUTES);
                    IFatVisitRecordService fatVisitRecordService = SpringUtils.getBean(IFatVisitRecordService.class);
                    FatVisitRecord byId = fatVisitRecordService.getById(visitId);
                    if (byId == null)
                        throw new RuntimeException("没有找到对应的会话记录");
                    IFatModelSetService fatModelSetService = SpringUtils.getBean(IFatModelSetService.class);
                    LambdaQueryWrapper<FatModelSet> modelSetLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    modelSetLambdaQueryWrapper.eq(FatModelSet::getUnitId, byId.getUnitId());
                    modelSetLambdaQueryWrapper.eq(FatModelSet::getPromptType, "advice");
                    modelSetLambdaQueryWrapper.last("limit 1");
                    FatModelSet one = fatModelSetService.getOne(modelSetLambdaQueryWrapper);
                    // 设置进度为30%
                    redisCache.setCacheObject(progressKey, "正在整理参数", 300, java.util.concurrent.TimeUnit.MINUTES);
                    if (one != null) {
                        String prompt = one.getPrompt();
                        String speechList = byId.getSpeechList();
                        prompt += "\n请基于以下医患对话开展分析\n" + speechList;

                        // 查询问卷
                        ICaszExamResultService caszExamResultService = SpringUtils
                                .getBean(ICaszExamResultService.class);
                        LambdaQueryWrapper<CaszExamResult> caszExamResultLambdaQueryWrapper = new LambdaQueryWrapper<>();
                        caszExamResultLambdaQueryWrapper.eq(CaszExamResult::getPatientId, byId.getPatientId());
                        caszExamResultLambdaQueryWrapper.orderByDesc(BaseEntity::getCreateTime);
//                        caszExamResultLambdaQueryWrapper.eq(CaszExamResult::getFormId, "1938077830100848641");
                        caszExamResultLambdaQueryWrapper.eq(CaszExamResult::getFormId, "1898574044028858369");
                        caszExamResultLambdaQueryWrapper.last("limit 1");
                        CaszExamResult one1 = caszExamResultService.getOne(caszExamResultLambdaQueryWrapper);
                        ICaszDynamicFormService dynamicFormService = SpringUtils.getBean(ICaszDynamicFormService.class);
//                        CaszDynamicForm jzForm = dynamicFormService.getById("1938077830100848641");
                        CaszDynamicForm jzForm = dynamicFormService.getById("1898574044028858369");
                        String idMap = "{}";
                        if (jzForm != null) {
                            idMap = jzForm.getIdMap();
                        }
                        String exam = null;
                        if (one1 != null) {
                            exam = FormTextValueUtils.transformJsonToChineseDisplay(one1.getFormValue(), idMap);
                        }

                        if (StringUtils.isNotEmpty(exam)) {
                            prompt += "\n患者最近一次就诊问卷\n" + exam;
                        }

                        if (StringUtils.isNotEmpty(otherRemarks)) {
                            prompt += "\n其他备注\n" + otherRemarks;
                        }
                        System.out.println(prompt);
                        Map<String, String> thoughtAndContent = AIUtils.getThoughtAndContent(one.getSysPrompt(),
                                prompt);
                        String call = thoughtAndContent.get("content").toString();
                        String thought = thoughtAndContent.get("thought").toString();
                        System.out.println("模型回复\n" + call);
                        try {
                            call = call.replace("```json", "").replace("```", "");
                            call = JsonSanitizer.sanitize(call);

                            JSONObject.parseObject(call);
                            byId.setAdvice(call);
                            byId.setUpdateTime(new Date());
                            byId.setAdviceThought(thought);
                            byId.setAdvicePrompt(prompt);
                            fatVisitRecordService.updateById(byId);

                            // 设置进度为100%并在5秒后删除
                            redisCache.setCacheObject(progressKey, "ai分析完成", 5, java.util.concurrent.TimeUnit.SECONDS);
                        } catch (Exception e) {
                            // 出错时删除进度
                            redisCache.deleteObject(progressKey);
                            e.printStackTrace();
                        }
                    }
                } catch (Exception e) {
                    // 出错时删除进度
                    RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
                    String progressKey = "advice_progress_sync:" + visitId;
                    redisCache.deleteObject(progressKey);
                    e.printStackTrace();
                    throw new RuntimeException(e);
                }
            }
        };
    }

    public static TimerTask adviceSplit(String visitId, String otherRemarks) {
        return new TimerTask() {
            @Override
            public void run() {
                // 添加进度条处理
                RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
                String progressKey = "advice_progress:" + visitId;
                try {
                    // 设置初始进度
                    redisCache.setCacheObject(progressKey, "准备分析中", 300, java.util.concurrent.TimeUnit.MINUTES);

                    IFatVisitRecordService fatVisitRecordService = SpringUtils.getBean(IFatVisitRecordService.class);
                    FatVisitRecord byId = fatVisitRecordService.getById(visitId);
                    if (byId == null)
                        throw new RuntimeException("没有找到对应的会话记录");
                    IFatModelSetService fatModelSetService = SpringUtils.getBean(IFatModelSetService.class);
                    LambdaQueryWrapper<FatModelSet> modelSetLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    modelSetLambdaQueryWrapper.eq(FatModelSet::getUnitId, byId.getUnitId());
                    modelSetLambdaQueryWrapper.eq(FatModelSet::getPromptType, "adviceMain");
                    modelSetLambdaQueryWrapper.last("limit 1");
                    FatModelSet one = fatModelSetService.getOne(modelSetLambdaQueryWrapper);

                    // 设置进度为30%
                    redisCache.setCacheObject(progressKey, "30", 300, java.util.concurrent.TimeUnit.MINUTES);

                    modelSetLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    modelSetLambdaQueryWrapper.eq(FatModelSet::getUnitId, byId.getUnitId());
                    modelSetLambdaQueryWrapper.eq(FatModelSet::getPromptType, "adviceDanger");
                    modelSetLambdaQueryWrapper.last("limit 1");
                    FatModelSet two = fatModelSetService.getOne(modelSetLambdaQueryWrapper);

                    if (one != null) {
                        String prompt = one.getPrompt();
                        String promptTwo = two.getPrompt();
                        String speechList = byId.getSpeechList();
                        prompt += "\n请基于以下医患对话开展分析\n" + speechList;
                        promptTwo += "\n请基于以下医患对话开展分析\n" + speechList;

                        // 查询问卷
                        ICaszExamResultService caszExamResultService = SpringUtils
                                .getBean(ICaszExamResultService.class);
                        LambdaQueryWrapper<CaszExamResult> caszExamResultLambdaQueryWrapper = new LambdaQueryWrapper<>();
                        caszExamResultLambdaQueryWrapper.eq(CaszExamResult::getPatientId, byId.getPatientId());
                        caszExamResultLambdaQueryWrapper.orderByDesc(BaseEntity::getCreateTime);
                        caszExamResultLambdaQueryWrapper.eq(CaszExamResult::getFormId, "1898574044028858369");
                        caszExamResultLambdaQueryWrapper.last("limit 1");
                        CaszExamResult one1 = caszExamResultService.getOne(caszExamResultLambdaQueryWrapper);
                        ICaszDynamicFormService dynamicFormService = SpringUtils.getBean(ICaszDynamicFormService.class);
                        CaszDynamicForm jzForm = dynamicFormService.getById("1898574044028858369");
                        String idMap = "{}";
                        redisCache.setCacheObject(progressKey, "ai正在全力分析中", 300, java.util.concurrent.TimeUnit.MINUTES);
                        if (jzForm != null) {
                            idMap = jzForm.getIdMap();
                        }
                        String exam = null;
                        if (one1 != null) {
                            exam = FormTextValueUtils.transformJsonToChineseDisplay(one1.getFormValue(), idMap);
                        }

                        if (StringUtils.isNotEmpty(exam)) {
                            prompt += "\n患者最近一次就诊问卷\n" + exam;
                            promptTwo += "\n患者最近一次就诊问卷\n" + exam;
                        }

                        if (StringUtils.isNotEmpty(otherRemarks)) {
                            prompt += "\n其他备注\n" + otherRemarks;
                            promptTwo += "\n患者最近一次就诊问卷\n" + exam;
                        }
                        System.out.println(prompt);

                        // 设置进度为50%
                        redisCache.setCacheObject(progressKey, "正在全力分析中", 300, java.util.concurrent.TimeUnit.MINUTES);

                        final String threadPrompt = prompt; // 提示词1
                        final String threadPromptTwo = promptTwo; // 提示词2
                        // 并行调用AI
                        CompletableFuture<String> future1 = CompletableFuture.supplyAsync(() -> {
                            try {
                                System.out.println("系统提示词" + one.getSysPrompt() + "主体提示词：" + threadPrompt);
                                return AIUtils.getThoughtAndContentByAgent(one.getSysPrompt(), threadPrompt);
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        });
                        CompletableFuture<String> future2 = CompletableFuture.supplyAsync(() -> {
                            try {
                                System.out.println("对话副提示词" + threadPromptTwo);
                                return AIUtils.getThoughtAndContentByAgent(two.getSysPrompt(), threadPromptTwo);
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        });

                        // 设置进度为70%
                        redisCache.setCacheObject(progressKey, "正在整理分析结果", 300, java.util.concurrent.TimeUnit.MINUTES);

                        future1.thenAcceptBoth(future2, (thoughtAndContent, eatCallContent) -> {
                            // 处理 result1 和 result2
                            String call = thoughtAndContent;
                            String thought = "";
                            String callPlan = eatCallContent;
                            // String callPlanTh = eatCallContent.get("thought");
                            System.out.println("对话主体方案回复\n" + call);
                            System.out.println("对话计划方案回复\n" + callPlan);
                            if (StringUtils.isNotEmpty(call)) {
                                call = call.replace("```json", "").replace("```", "");
                                call = JsonSanitizer.sanitize(call);
                            }

                            // 设置进度为90%
                            redisCache.setCacheObject(progressKey, "正在保存结果", 300,
                                    java.util.concurrent.TimeUnit.MINUTES);

                            // String eatCall = AIUtils.getSchemeEat(promptTwo);
                            if (StringUtils.isNotEmpty(callPlan)) {

                                callPlan = callPlan.replace("```json", "").replace("```", "");
                                JSONObject eatJson = JSONObject.parseObject(callPlan);
                                // 提取疾病风险和进一步检查建议
                                String diseaseRisk = eatJson.getString("疾病风险");
                                String furtherCheckAdvice = eatJson.getString("进一步检查建议");

                                // 解析 call
                                JSONObject callJson = JSONObject.parseObject(call);

                                // 替换 call 中的疾病风险和进一步检查建议字段
                                if (diseaseRisk != null) {
                                    callJson.put("疾病风险", diseaseRisk);
                                }

                                if (furtherCheckAdvice != null) {
                                    callJson.put("进一步检查建议", furtherCheckAdvice);
                                }
                                call = callJson.toJSONString();
                                System.out.println("合并后的方案内容\n" + call);
                            }

                            JSONObject.parseObject(call);
                            byId.setAdvice(call);
                            byId.setUpdateTime(new Date());
                            byId.setAdviceThought(thought);
                            byId.setAdvicePrompt(threadPrompt);
                            fatVisitRecordService.updateById(byId);

                            // 设置进度为100%并在5秒后删除
                            redisCache.setCacheObject(progressKey, "100", 5, java.util.concurrent.TimeUnit.SECONDS);

                            // 后续处理逻辑
                        }).exceptionally(ex -> {
                            // 处理异常，清除进度
                            redisCache.deleteObject(progressKey);
                            ex.printStackTrace();
                            return null;
                        });
                    }
                } catch (Exception e) {
                    // 处理异常，清除进度
                    redisCache.deleteObject(progressKey);
                    e.printStackTrace();
                    throw new RuntimeException(e);
                }
            }
        };
    }

    public static TimerTask analysisSplit(String visitId, String otherRemarks) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    executeAnalysisSplitWithRetry(visitId, otherRemarks, 0, 3);
                } catch (Exception e) {
                    // TimerTask中的异常必须在这里处理，否则会被线程池吞掉
                    im_logger.error("analysisSplit TimerTask 执行失败，visitId: {}, 错误: {}", visitId, e.getMessage(), e);

                    // 删除进度记录
                    try {
                        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
                        String progressKey = "analysis_progress:" + visitId;
                        redisCache.deleteObject(progressKey);
                    } catch (Exception redisEx) {
                        im_logger.error("删除进度记录失败，visitId: {}, 错误: {}", visitId, redisEx.getMessage(), redisEx);
                    }

                    // 由于在TimerTask中，异常无法向外传播，所以记录最终失败
                    im_logger.error("analysisSplit 最终失败，不再重试，visitId: {}", visitId);
                }
            }
        };
    }

    /**
     * 带重试机制的 analysisSplit 执行方法
     *
     * @param visitId        访问ID
     * @param otherRemarks   其他备注
     * @param currentAttempt 当前尝试次数
     * @param maxRetries     最大重试次数
     */
    private static void executeAnalysisSplitWithRetry(String visitId, String otherRemarks, int currentAttempt,
            int maxRetries) {
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        String progressKey = "analysis_progress:" + visitId;
        try {
            // 设置初始进度
            redisCache.setCacheObject(progressKey, "正在准备病情分析", 300, java.util.concurrent.TimeUnit.MINUTES);
            IFatVisitRecordService fatVisitRecordService = SpringUtils.getBean(IFatVisitRecordService.class);
            FatVisitRecord byId = fatVisitRecordService.getById(visitId);
            if (byId == null)
                throw new RuntimeException("没有找到对应的就诊记录");

            // 设置进度为10%
            redisCache.setCacheObject(progressKey, "准备分析中", 300, java.util.concurrent.TimeUnit.MINUTES);

            // 查询问卷
            ICaszExamResultService caszExamResultService = SpringUtils.getBean(ICaszExamResultService.class);
            LambdaQueryWrapper<CaszExamResult> caszExamResultLambdaQueryWrapper = new LambdaQueryWrapper<>();
            caszExamResultLambdaQueryWrapper.eq(CaszExamResult::getPatientId, byId.getPatientId());
            caszExamResultLambdaQueryWrapper.orderByDesc(BaseEntity::getCreateTime);
            caszExamResultLambdaQueryWrapper.eq(CaszExamResult::getFormId, "1898574044028858369");
            caszExamResultLambdaQueryWrapper.last("limit 1");
            CaszExamResult one1 = caszExamResultService.getOne(caszExamResultLambdaQueryWrapper);
            ICaszDynamicFormService dynamicFormService = SpringUtils.getBean(ICaszDynamicFormService.class);
            CaszDynamicForm jzForm = dynamicFormService.getById("1898574044028858369");
            String idMap = "{}";
            if (jzForm != null) {
                idMap = jzForm.getIdMap();
            }
            String exam = null;
            if (one1 != null) {
                exam = FormTextValueUtils.transformJsonToChineseDisplay(one1.getFormValue(), idMap);
            }
            // 查询检测报告
            StringBuffer labCheck = new StringBuffer();
            IFatPictureService fatPictureService = SpringUtils.getBean(IFatPictureService.class);
            LambdaQueryWrapper<FatPicture> pictureLambdaQueryWrapper = new LambdaQueryWrapper<>();
            pictureLambdaQueryWrapper.eq(FatPicture::getJzId, visitId);
            List<FatPicture> list = fatPictureService.list(pictureLambdaQueryWrapper);
            if (!list.isEmpty()) {
                for (int i = 0; i < list.size(); i++) {
                    FatPicture fatPicture = list.get(i);
                    if (StringUtils.isNotEmpty(fatPicture.getCnText())) {
                        labCheck.append("\n检查报告").append(i + 1).append(":").append(fatPicture.getCnText());
                    }
                }
            }
            IFatModelSetService fatModelSetService = SpringUtils.getBean(IFatModelSetService.class);
            LambdaQueryWrapper<FatModelSet> modelSetLambdaQueryWrapper = new LambdaQueryWrapper<>();
            modelSetLambdaQueryWrapper.eq(FatModelSet::getUnitId, byId.getUnitId());
            modelSetLambdaQueryWrapper.eq(FatModelSet::getPromptType, "analysisMain");
            modelSetLambdaQueryWrapper.last("limit 1");
            FatModelSet one = fatModelSetService.getOne(modelSetLambdaQueryWrapper);

            modelSetLambdaQueryWrapper = new LambdaQueryWrapper<>();
            modelSetLambdaQueryWrapper.eq(FatModelSet::getUnitId, byId.getUnitId());
            modelSetLambdaQueryWrapper.eq(FatModelSet::getPromptType, "analysisPlan");
            modelSetLambdaQueryWrapper.last("limit 1");
            FatModelSet two = fatModelSetService.getOne(modelSetLambdaQueryWrapper);

            if (one != null) {
                String prompt = one.getPrompt();
                String promptTwo = two.getPrompt();
                prompt += "\n请基于以下医患对话开展分析";
                prompt += "\n门诊内容：" + byId.getAdvice();
                if (StringUtils.isNotEmpty(exam)) {
                    prompt += "\n问卷内容：" + exam;
                    promptTwo += "\n问卷内容：" + exam;
                }
                if (StringUtils.isNotEmpty(labCheck.toString())) {
                    prompt += labCheck.toString();
                    promptTwo += labCheck.toString();
                }
                if (StringUtils.isNotEmpty(otherRemarks)) {
                    prompt += "\n其他备注：" + otherRemarks;
                    promptTwo += "\n其他备注：" + otherRemarks;
                }

                final String threadPrompt = prompt; // 提示词1
                final String threadPromptTwo = promptTwo; // 提示词2

                // 设置进度为50%
                redisCache.setCacheObject(progressKey, "正在全力分析中", 300, java.util.concurrent.TimeUnit.MINUTES);

                // 并行调用AI
                CompletableFuture<String> future1 = CompletableFuture.supplyAsync(() -> {
                    try {
                        System.out.println("系统提示词" + one.getSysPrompt() + "主体提示词：" + threadPrompt);
                        return AIUtils.getThoughtAndContentByAgent(one.getSysPrompt(), threadPrompt);
                    } catch (Exception e) {
                        im_logger.error("AI调用失败 - future1, visitId: {}, 错误: {}", visitId, e.getMessage(), e);
                        throw new RuntimeException("AI分析失败: " + e.getMessage(), e);
                    }
                });
                CompletableFuture<String> future2 = CompletableFuture.supplyAsync(() -> {
                    try {
                        System.out.println("病情副提示词" + threadPromptTwo);
                        return AIUtils.getThoughtAndContentByAgent(two.getSysPrompt(), threadPromptTwo);
                    } catch (Exception e) {
                        im_logger.error("AI调用失败 - future2, visitId: {}, 错误: {}", visitId, e.getMessage(), e);
                        throw new RuntimeException("AI分析失败: " + e.getMessage(), e);
                    }
                });

                // 统一异常处理
                CompletableFuture<Void> combinedFuture = future1.thenAcceptBoth(future2, (thoughtAndContent, eatCallContent) -> {
                    String call = thoughtAndContent;
                    String thought = "";
                    String analysisPlan = eatCallContent;
                    System.out.println("模型回复\n" + call);
                    System.out.println("病情分析回复\n" + analysisPlan);
                    try {
                        // 设置进度为80%
                        redisCache.setCacheObject(progressKey, "整理回复中", 300, java.util.concurrent.TimeUnit.MINUTES);

                        call = call.replace("```json", "").replace("```", "");
                        call = JsonSanitizer.sanitize(call);
                        if (StringUtils.isNotEmpty(analysisPlan)) {

                            analysisPlan = analysisPlan.replace("```json", "").replace("```", "");
                            JSONObject analysisPlanJson = JSONObject.parseObject(analysisPlan);

                            // 提取疾病风险和进一步检查建议
                            String diseaseRisk = analysisPlanJson.getString("多学科评估与诊断");
                            String furtherCheckAdvice = analysisPlanJson.getString("治疗计划安排");

                            // 解析 call
                            JSONObject callJson = JSONObject.parseObject(call);

                            // 替换 call 中的疾病风险和进一步检查建议字段
                            if (diseaseRisk != null) {
                                callJson.put("多学科评估与诊断", diseaseRisk);
                            }

                            if (furtherCheckAdvice != null) {
                                callJson.put("治疗计划安排", furtherCheckAdvice);
                            }

                            call = callJson.toJSONString();
                            System.out.println("合并后的方案内容\n" + call);
                        }

                        byId.setAnalysis(call);
                        byId.setUpdateTime(new Date());
                        byId.setAnalysisThought(thought);
                        byId.setAnalysisPrompt(threadPrompt);
                        fatVisitRecordService.updateById(byId);

                        // 设置进度为100%并删除Redis中的进度记录
                        redisCache.setCacheObject(progressKey, "100", 5, java.util.concurrent.TimeUnit.SECONDS);
                        // 5秒后自动删除
                    } catch (Exception e) {
                        e.printStackTrace();
                        // 如果出错，删除进度记录
                        redisCache.deleteObject(progressKey);
                        throw new RuntimeException("JSON解析失败: " + e.getMessage(), e);
                    }
                });

                // 统一异常处理
                combinedFuture.exceptionally(ex -> {
                    // 如果出错，删除进度记录
                    redisCache.deleteObject(progressKey);

                    // 提取真实异常
                    Throwable cause = ex;
                    while (cause instanceof CompletionException && cause.getCause() != null) {
                        cause = cause.getCause();
                    }

                    im_logger.error("analysisSplit 异步执行失败，visitId: {}, 尝试次数: {}, 错误: {}", visitId, currentAttempt + 1,
                            cause.getMessage(), cause);
                    handleAnalysisSplitRetry(visitId, otherRemarks, currentAttempt, maxRetries, cause);
                    return null;
                });

            }
        } catch (Exception e) {
            // 如果出错，删除进度记录
            redisCache.deleteObject(progressKey);
            im_logger.error("analysisSplit 执行出错，visitId: {}, 尝试次数: {}, 错误: {}", visitId, currentAttempt + 1, e.getMessage(), e);
            handleAnalysisSplitRetry(visitId, otherRemarks, currentAttempt, maxRetries, e);
        }
    }

    /**
     * 处理 analysisSplit 重试逻辑
     */
    private static void handleAnalysisSplitRetry(String visitId, String otherRemarks, int currentAttempt, int maxRetries, Throwable error) {
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        String progressKey = "analysis_progress:" + visitId;

        if (currentAttempt < maxRetries - 1) {
            int nextAttempt = currentAttempt + 1;
            long delayMs = 2000L * (1L << nextAttempt); // 指数退避：2秒、4秒、8秒
            im_logger.warn("analysisSplit 第{}次尝试失败，{}ms后进行第{}次重试，visitId: {}",
                    currentAttempt + 1, delayMs, nextAttempt + 1, visitId);

            // 设置重试等待进度
            redisCache.setCacheObject(progressKey, "分析失败，准备重试中", 300, java.util.concurrent.TimeUnit.MINUTES);

            Timer retryTimer = new Timer();
            retryTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    executeAnalysisSplitWithRetry(visitId, otherRemarks, nextAttempt, maxRetries);
                }
            }, delayMs);
        } else {
            // 最终失败，删除进度记录
            redisCache.deleteObject(progressKey);
            im_logger.error("analysisSplit 重试{}次后仍然失败，终止执行，visitId: {}, 最终错误: {}",
                    maxRetries, visitId, error.getMessage(), error);
        }
    }

    /**
     * 病情分析后台运行
     *
     * @return 任务task
     */
    public static TimerTask analysis(String visitId, String otherRemarks) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    executeAnalysisWithRetry(visitId, otherRemarks, 0, 3);
                } catch (Exception e) {
                    // TimerTask中的异常必须在这里处理，否则会被线程池吞掉
                    im_logger.error("analysis TimerTask 执行失败，visitId: {}, 错误: {}", visitId, e.getMessage(), e);

                    // 删除进度记录
                    try {
                        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
                        String progressKey = "analysis_progress:" + visitId;
                        redisCache.deleteObject(progressKey);
                    } catch (Exception redisEx) {
                        im_logger.error("删除进度记录失败，visitId: {}, 错误: {}", visitId, redisEx.getMessage(), redisEx);
                    }

                    // 由于在TimerTask中，异常无法向外传播，所以记录最终失败
                    im_logger.error("analysis 最终失败，不再重试，visitId: {}", visitId);
                }
            }
        };
    }

    /**
     * 带重试机制的 analysis 执行方法
     *
     * @param visitId        访问ID
     * @param otherRemarks   其他备注
     * @param currentAttempt 当前尝试次数
     * @param maxRetries     最大重试次数
     */
    private static void executeAnalysisWithRetry(String visitId, String otherRemarks, int currentAttempt,
            int maxRetries) {
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        String progressKey = "analysis_progress:" + visitId;
        try {
            // 设置初始进度
            redisCache.setCacheObject(progressKey, "正在准备病情分析", 300, java.util.concurrent.TimeUnit.MINUTES);

            IFatVisitRecordService fatVisitRecordService = SpringUtils.getBean(IFatVisitRecordService.class);
            FatVisitRecord byId = fatVisitRecordService.getById(visitId);
            if (byId == null) {
                throw new RuntimeException("没有找到对应的就诊记录");
            }

            // 设置进度为10%
            redisCache.setCacheObject(progressKey, "准备分析中", 300, java.util.concurrent.TimeUnit.MINUTES);
            // 查询问卷
            ICaszExamResultService caszExamResultService = SpringUtils.getBean(ICaszExamResultService.class);
            LambdaQueryWrapper<CaszExamResult> caszExamResultLambdaQueryWrapper = new LambdaQueryWrapper<>();
            caszExamResultLambdaQueryWrapper.eq(CaszExamResult::getPatientId, byId.getPatientId());
            caszExamResultLambdaQueryWrapper.orderByDesc(BaseEntity::getCreateTime);
            caszExamResultLambdaQueryWrapper.eq(CaszExamResult::getFormId, "1898574044028858369");
            caszExamResultLambdaQueryWrapper.last("limit 1");
            CaszExamResult one1 = caszExamResultService.getOne(caszExamResultLambdaQueryWrapper);
            ICaszDynamicFormService dynamicFormService = SpringUtils.getBean(ICaszDynamicFormService.class);
            CaszDynamicForm jzForm = dynamicFormService.getById("1898574044028858369");
            String idMap = "{}";
            if (jzForm != null) {
                idMap = jzForm.getIdMap();
            }
            String exam = null;
            if (one1 != null) {
                exam = FormTextValueUtils.transformJsonToChineseDisplay(one1.getFormValue(), idMap);
            }
            // 查询检测报告
            StringBuffer labCheck = new StringBuffer();
            IFatPictureService fatPictureService = SpringUtils.getBean(IFatPictureService.class);
            LambdaQueryWrapper<FatPicture> pictureLambdaQueryWrapper = new LambdaQueryWrapper<>();
            pictureLambdaQueryWrapper.eq(FatPicture::getJzId, visitId);
            List<FatPicture> list = fatPictureService.list(pictureLambdaQueryWrapper);
            if (!list.isEmpty()) {
                for (int i = 0; i < list.size(); i++) {
                    FatPicture fatPicture = list.get(i);
                    if (StringUtils.isNotEmpty(fatPicture.getCnText())) {
                        labCheck.append("\n检查报告").append(i + 1).append(":").append(fatPicture.getCnText());
                    }
                }
            }
            IFatModelSetService fatModelSetService = SpringUtils.getBean(IFatModelSetService.class);
            LambdaQueryWrapper<FatModelSet> modelSetLambdaQueryWrapper = new LambdaQueryWrapper<>();
            modelSetLambdaQueryWrapper.eq(FatModelSet::getUnitId, byId.getUnitId());
            modelSetLambdaQueryWrapper.eq(FatModelSet::getPromptType, "analysis");
            modelSetLambdaQueryWrapper.last("limit 1");
            FatModelSet one = fatModelSetService.getOne(modelSetLambdaQueryWrapper);
            if (one != null) {
                // 设置进度为30%
                redisCache.setCacheObject(progressKey, "30", 300, java.util.concurrent.TimeUnit.MINUTES);

                String prompt = one.getPrompt();
                prompt += "\n请基于以下医患对话开展分析";
                prompt += "\n门诊内容：" + byId.getAdvice();
                if (StringUtils.isNotEmpty(exam)) {
                    prompt += "\n问卷内容：" + exam;
                }
                if (StringUtils.isNotEmpty(labCheck.toString())) {
                    prompt += labCheck.toString();
                }
                if (StringUtils.isNotEmpty(otherRemarks)) {
                    prompt += "\n其他备注：" + otherRemarks;
                }

                // 设置进度为50%
                redisCache.setCacheObject(progressKey, "正在全力分析中", 300, java.util.concurrent.TimeUnit.MINUTES);

                System.out.println("完整提示词：" + prompt);
                // String call = AIUtils.call(one.getSysPrompt(), prompt);
                Map<String, String> thoughtAndContent = AIUtils.getThoughtAndContent(one.getSysPrompt(),
                        prompt);

                // 设置进度为80%
                redisCache.setCacheObject(progressKey, "整理回复中", 300, java.util.concurrent.TimeUnit.MINUTES);

                String call = thoughtAndContent.get("content").toString();
                String thought = thoughtAndContent.get("thought").toString();
                System.out.println("模型回复\n" + call);
                try {
                    call = call.replace("```json", "").replace("```", "");
                    call = JsonSanitizer.sanitize(call);
                    JSONObject.parseObject(call);
                    byId.setAnalysis(call);
                    byId.setUpdateTime(new Date());
                    byId.setAnalysisThought(thought);
                    byId.setAnalysisPrompt(prompt);
                    fatVisitRecordService.updateById(byId);

                    // 设置进度为100%并删除Redis中的进度记录
                    redisCache.setCacheObject(progressKey, "100", 5, java.util.concurrent.TimeUnit.SECONDS);
                    // 5秒后自动删除
                } catch (Exception e) {
                    e.printStackTrace();
                    // 如果出错，删除进度记录
                    redisCache.deleteObject(progressKey);
                    throw new RuntimeException("JSON解析失败: " + e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            // 如果出错，删除进度记录
            redisCache.deleteObject(progressKey);
            im_logger.error("analysis 执行出错，visitId: {}, 尝试次数: {}, 错误: {}", visitId, currentAttempt + 1, e.getMessage(), e);
            handleAnalysisRetry(visitId, otherRemarks, currentAttempt, maxRetries, e);
        }
    }

    /**
     * 处理 analysis 重试逻辑
     */
    private static void handleAnalysisRetry(String visitId, String otherRemarks, int currentAttempt, int maxRetries, Throwable error) {
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        String progressKey = "analysis_progress:" + visitId;

        if (currentAttempt < maxRetries - 1) {
            int nextAttempt = currentAttempt + 1;
            long delayMs = 2000L * (1L << nextAttempt); // 指数退避：2秒、4秒、8秒
            im_logger.warn("analysis 第{}次尝试失败，{}ms后进行第{}次重试，visitId: {}",
                    currentAttempt + 1, delayMs, nextAttempt + 1, visitId);

            // 设置重试等待进度
            redisCache.setCacheObject(progressKey, "分析失败，准备重试中", 300, java.util.concurrent.TimeUnit.MINUTES);

            Timer retryTimer = new Timer();
            retryTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    executeAnalysisWithRetry(visitId, otherRemarks, nextAttempt, maxRetries);
                }
            }, delayMs);
        } else {
            // 最终失败，删除进度记录
            redisCache.deleteObject(progressKey);
            im_logger.error("analysis 重试{}次后仍然失败，终止执行，visitId: {}, 最终错误: {}",
                    maxRetries, visitId, error.getMessage(), error);
        }
    }

    public static TimerTask scheme(String visitId, String otherRemarks) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    IFatVisitRecordService fatVisitRecordService = SpringUtils.getBean(IFatVisitRecordService.class);
                    FatVisitRecord byId = fatVisitRecordService.getById(visitId);
                    if (byId == null) {
                        throw new RuntimeException("没有找到对应的就诊记录");
                    }
                    // 查询问卷
                    ICaszExamResultService caszExamResultService = SpringUtils.getBean(ICaszExamResultService.class);
                    LambdaQueryWrapper<CaszExamResult> caszExamResultLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    caszExamResultLambdaQueryWrapper.eq(CaszExamResult::getPatientId, byId.getPatientId());
                    caszExamResultLambdaQueryWrapper.orderByDesc(BaseEntity::getCreateTime);
                    caszExamResultLambdaQueryWrapper.eq(CaszExamResult::getFormId, "1898574044028858369");
                    caszExamResultLambdaQueryWrapper.last("limit 1");
                    CaszExamResult one1 = caszExamResultService.getOne(caszExamResultLambdaQueryWrapper);
                    ICaszDynamicFormService dynamicFormService = SpringUtils.getBean(ICaszDynamicFormService.class);
                    CaszDynamicForm jzForm = dynamicFormService.getById("1898574044028858369");
                    String idMap = "{}";
                    if (jzForm != null) {
                        idMap = jzForm.getIdMap();
                    }
                    String exam = FormTextValueUtils.transformJsonToChineseDisplay(one1.getFormValue(), idMap);
                    // 查询检测报告
                    StringBuffer labCheck = new StringBuffer();
                    IFatPictureService fatPictureService = SpringUtils.getBean(IFatPictureService.class);
                    LambdaQueryWrapper<FatPicture> pictureLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    pictureLambdaQueryWrapper.eq(FatPicture::getJzId, visitId);
                    List<FatPicture> list = fatPictureService.list(pictureLambdaQueryWrapper);
                    if (!list.isEmpty()) {
                        for (int i = 0; i < list.size(); i++) {
                            FatPicture fatPicture = list.get(i);
                            if (StringUtils.isNotEmpty(fatPicture.getCnText())) {
                                labCheck.append("\n检查报告").append(i + 1).append(":").append(fatPicture.getCnText());
                            }
                        }
                    }
                    IFatModelSetService fatModelSetService = SpringUtils.getBean(IFatModelSetService.class);
                    LambdaQueryWrapper<FatModelSet> modelSetLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    modelSetLambdaQueryWrapper.eq(FatModelSet::getUnitId, byId.getUnitId());
                    modelSetLambdaQueryWrapper.eq(FatModelSet::getPromptType, "schemeHealth");
                    modelSetLambdaQueryWrapper.last("limit 1");
                    FatModelSet one = fatModelSetService.getOne(modelSetLambdaQueryWrapper);
                    if (one != null) {
                        String prompt = one.getPrompt();
                        prompt += "\n请基于以下医患对话开展分析";
                        prompt += "\n门诊内容：" + byId.getAdvice();
                        if (StringUtils.isNotEmpty(exam)) {
                            prompt += "\n问卷内容：" + exam;
                        }
                        if (StringUtils.isNotEmpty(labCheck.toString())) {
                            prompt += labCheck.toString();
                        }
                        if (StringUtils.isNotEmpty(otherRemarks)) {
                            prompt += "\n" + otherRemarks;
                        }
                        System.out.println(prompt);
                        // String call = AIUtils.call(one.getSysPrompt(), prompt);
                        Map<String, String> thoughtAndContent = AIUtils.getThoughtAndContent(one.getSysPrompt(),
                                prompt);
                        String call = thoughtAndContent.get("content");
                        String thought = thoughtAndContent.get("thought");
                        System.out.println("模型回复\n" + call);
                        try {
                            call = call.replace("```json", "").replace("```", "");
                            call = JsonSanitizer.sanitize(call);
                            JSONObject.parseObject(call);

                            // 第二次分析，调整热量
                            // String adjustPrompt =
                            // call+"单日热量值不符合1700kcal请调整食物重量或食物类型使之符合约1700的热量值，调整后的热量可以允许略微超过1700kcal，请思考到符合要求的数据之后在输出结果，结果仍以json格式输出";
                            // Map<String, StringBuilder> adjust = AIUtils.call("", adjustPrompt);
                            // String adjustCall = adjust.get("content").toString();
                            // adjustCall = adjustCall.replace("```json", "").replace("```", "");
                            // adjustCall = JsonSanitizer.sanitize(adjustCall);
                            // JSONObject.parseObject(adjustCall);

                            CaszHealthIntervene healthIntervene = new CaszHealthIntervene();
                            healthIntervene.setCreateTime(new Date());
                            healthIntervene.setThought(thought);
                            healthIntervene.setPatientId(byId.getPatientId());
                            healthIntervene.setStatus("0");
                            healthIntervene.setHosId(byId.getUnitId());
                            healthIntervene.setDocterId(byId.getDoctorUserId());
                            healthIntervene.setAcceptStatus("Y");
                            healthIntervene.setStartDate(new Date());
                            healthIntervene.setEndDate(DateUtils.getFutureDate(new Date(), 90));
                            healthIntervene.setDelFlag("0");
                            healthIntervene.setPrompt(prompt);
                            ICaszHealthInterveneService healthInterveneService = SpringUtils
                                    .getBean(ICaszHealthInterveneService.class);
                            healthInterveneService.save(healthIntervene);

                            CaszScreenForm screenForm = new CaszScreenForm();
                            screenForm.setCreateTime(new Date());
                            screenForm.setFormValue(call);
                            // screenForm.setFormValue(adjustCall);
                            screenForm.setHosId(byId.getUnitId());
                            screenForm.setPatientId(byId.getPatientId());
                            screenForm.setScreeningId(healthIntervene.getId());
                            screenForm.setDelFlag("0");
                            screenForm.setIdNo(byId.getIdNo());
                            screenForm.setDataSource("1");
                            screenForm.setFormId(jzForm.getId());
                            screenForm.setFormText(jzForm.getFormDesignerText());
                            screenForm.setFormKey(jzForm.getFormKey());
                            screenForm.setFormName(jzForm.getFormName());

                            ICaszScreenFormService screenFormService = SpringUtils
                                    .getBean(ICaszScreenFormService.class);
                            screenFormService.save(screenForm);

                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException(e);
                }
            }
        };
    }

    public static TimerTask schemeNew(String visitId, String otherRemarks) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    executeSchemeNewWithRetry(visitId, otherRemarks, 0, 3);
                } catch (Exception e) {
                    // TimerTask中的异常必须在这里处理，否则会被线程池吞掉
                    im_logger.error("schemeNew TimerTask 执行失败，visitId: {}, 错误: {}", visitId, e.getMessage(), e);

                    // 删除进度记录
                    try {
                        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
                        String progressKey = "scheme_progress:" + visitId;
                        redisCache.deleteObject(progressKey);
                    } catch (Exception redisEx) {
                        im_logger.error("删除进度记录失败，visitId: {}, 错误: {}", visitId, redisEx.getMessage(), redisEx);
                    }

                    // 由于在TimerTask中，异常无法向外传播，所以记录最终失败
                    im_logger.error("schemeNew 最终失败，不再重试，visitId: {}", visitId);
                }
            }
        };
    }

    /**
     * 带重试机制的 schemeNew 执行方法
     *
     * @param visitId        访问ID
     * @param otherRemarks   其他备注
     * @param currentAttempt 当前尝试次数
     * @param maxRetries     最大重试次数
     */
    private static void executeSchemeNewWithRetry(String visitId, String otherRemarks, int currentAttempt,
            int maxRetries) {
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        String progressKey = "scheme_progress:" + visitId;
        try {
            // 设置初始进度
            redisCache.setCacheObject(progressKey, "正在准备干预方案", 300, java.util.concurrent.TimeUnit.MINUTES);

            im_logger.info("schemeNew 开始执行，visitId: {}, 当前尝试次数: {}/{}", visitId, currentAttempt + 1, maxRetries);
            IFatVisitRecordService fatVisitRecordService = SpringUtils.getBean(IFatVisitRecordService.class);
            FatVisitRecord byId = fatVisitRecordService.getById(visitId);
            if (byId == null) {
                throw new RuntimeException("没有找到对应的就诊记录");
            }

            // 设置进度：准备数据
            redisCache.setCacheObject(progressKey, "正在收集患者信息", 300, java.util.concurrent.TimeUnit.MINUTES);
            // 查询问卷
            ICaszExamResultService caszExamResultService = SpringUtils.getBean(ICaszExamResultService.class);
            LambdaQueryWrapper<CaszExamResult> caszExamResultLambdaQueryWrapper = new LambdaQueryWrapper<>();
            caszExamResultLambdaQueryWrapper.eq(CaszExamResult::getPatientId, byId.getPatientId());
            caszExamResultLambdaQueryWrapper.orderByDesc(BaseEntity::getCreateTime);
            caszExamResultLambdaQueryWrapper.eq(CaszExamResult::getFormId, "1898574044028858369");
            caszExamResultLambdaQueryWrapper.last("limit 1");
            CaszExamResult one1 = caszExamResultService.getOne(caszExamResultLambdaQueryWrapper);
            ICaszDynamicFormService dynamicFormService = SpringUtils.getBean(ICaszDynamicFormService.class);
            CaszDynamicForm jzForm = dynamicFormService.getById("1898574044028858369");
            String idMap = "{}";
            if (jzForm != null) {
                idMap = jzForm.getIdMap();
            }
            String exam = FormTextValueUtils.transformJsonToChineseDisplay(one1.getFormValue(), idMap);
            // 查询检测报告
            StringBuffer labCheck = new StringBuffer();
            IFatPictureService fatPictureService = SpringUtils.getBean(IFatPictureService.class);
            LambdaQueryWrapper<FatPicture> pictureLambdaQueryWrapper = new LambdaQueryWrapper<>();
            pictureLambdaQueryWrapper.eq(FatPicture::getJzId, visitId);
            List<FatPicture> list = fatPictureService.list(pictureLambdaQueryWrapper);
            if (!list.isEmpty()) {
                for (int i = 0; i < list.size(); i++) {
                    FatPicture fatPicture = list.get(i);
                    if (StringUtils.isNotEmpty(fatPicture.getCnText())) {
                        labCheck.append("\n检查报告").append(i + 1).append(":").append(fatPicture.getCnText());
                    }
                }
            }
            IFatModelSetService fatModelSetService = SpringUtils.getBean(IFatModelSetService.class);
            LambdaQueryWrapper<FatModelSet> modelSetLambdaQueryWrapper = new LambdaQueryWrapper<>();
            modelSetLambdaQueryWrapper.eq(FatModelSet::getUnitId, byId.getUnitId());
            modelSetLambdaQueryWrapper.eq(FatModelSet::getPromptType, "schemeHealth");
            modelSetLambdaQueryWrapper.last("limit 1");
            FatModelSet one = fatModelSetService.getOne(modelSetLambdaQueryWrapper);

            modelSetLambdaQueryWrapper = new LambdaQueryWrapper<>();
            modelSetLambdaQueryWrapper.eq(FatModelSet::getUnitId, byId.getUnitId());
            modelSetLambdaQueryWrapper.eq(FatModelSet::getPromptType, "schemeEat");
            modelSetLambdaQueryWrapper.last("limit 1");
            FatModelSet two = fatModelSetService.getOne(modelSetLambdaQueryWrapper);
            if (one != null) {
                // 设置进度：构建提示词
                redisCache.setCacheObject(progressKey, "正在构建分析提示词", 300, java.util.concurrent.TimeUnit.MINUTES);

                String prompt = one.getPrompt();
                prompt += "\n请基于以下医患对话开展分析";
                prompt += "\n门诊内容：" + byId.getAdvice();
                if (StringUtils.isNotEmpty(exam)) {
                    prompt += "\n问卷内容：" + exam;
                }
                if (StringUtils.isNotEmpty(labCheck.toString())) {
                    prompt += labCheck.toString();
                }

                String promptTwo = two.getPrompt();
                if (StringUtils.isNotEmpty(otherRemarks.toString())) {
                    promptTwo += otherRemarks.toString();
                }
                final String threadPrompt = prompt; // 提示词1
                final String threadPromptTwo = promptTwo; // 提示词2

                // 设置进度：开始AI分析
                redisCache.setCacheObject(progressKey, "正在全力分析干预方案", 300, java.util.concurrent.TimeUnit.MINUTES);

                // 并行调用AI - 修复异常处理
                CompletableFuture<Map<String, String>> future1 = CompletableFuture.supplyAsync(() -> {
                    try {
                        System.out.println("系统提示词" + one.getSysPrompt() + "主体提示词：" + threadPrompt);
                        return AIUtils.getThoughtAndContent(one.getSysPrompt(), threadPrompt);
                    } catch (Exception e) {
                        im_logger.error("AI调用失败 - future1, visitId: {}, 错误: {}", visitId, e.getMessage(), e);
                        throw new RuntimeException("AI分析失败: " + e.getMessage(), e);
                    }
                });

                CompletableFuture<String> future2 = CompletableFuture.supplyAsync(() -> {
                    try {
                        System.out.println("饮食提示词" + threadPromptTwo);
                        return AIUtils.getSchemeEat(threadPromptTwo);
                    } catch (Exception e) {
                        im_logger.error("AI调用失败 - future2, visitId: {}, 错误: {}", visitId, e.getMessage(), e);
                        throw new RuntimeException("饮食方案生成失败: " + e.getMessage(), e);
                    }
                });

                // 统一的异常处理
                CompletableFuture<Void> combinedFuture = future1.thenCombine(future2,
                        (thoughtAndContent, eatCallContent) -> {
                            try {
                                // 设置进度：整理分析结果
                                redisCache.setCacheObject(progressKey, "正在整理干预方案结果", 300,
                                        java.util.concurrent.TimeUnit.MINUTES);

                                // 处理 result1 和 result2
                                String call = thoughtAndContent.get("content");
                                String thought = thoughtAndContent.get("thought");
                                String eatCall = eatCallContent;
                                System.out.println("主体方案回复\n" + call);
                                System.out.println("饮食方案回复\n" + eatCall);
                                if (StringUtils.isNotEmpty(call)) {
                                    call = call.replace("```json", "").replace("```", "");
                                    call = JsonSanitizer.sanitize(call);
                                }

                                // String eatCall = AIUtils.getSchemeEat(promptTwo);
                                if (StringUtils.isNotEmpty(eatCall)) {

                                    eatCall = eatCall.replace("```json", "").replace("```", "");
                                    JSONObject eatJson = JSONObject.parseObject(eatCall);

                                    // 2. 转换为 Map 方便操作
                                    Map<String, Object> map = eatJson.to(LinkedHashMap.class);
                                    Map<String, Object> replacements = new HashMap<>();
                                    replacements.put("个性化饮食食谱", map.get(map.keySet().iterator().next())); // 把某个字段替换成
                                                                                                          // eatCall 的内容

                                    call = JsonUtils.replaceJsonValue(call, replacements);
                                }

                                CaszHealthIntervene healthIntervene = new CaszHealthIntervene();
                                healthIntervene.setCreateTime(new Date());
                                healthIntervene.setThought(thought);
                                healthIntervene.setPatientId(byId.getPatientId());
                                healthIntervene.setStatus("0");
                                healthIntervene.setHosId(byId.getUnitId());
                                healthIntervene.setDocterId(byId.getDoctorUserId());
                                healthIntervene.setAcceptStatus("Y");
                                healthIntervene.setDataSource("2");
                                healthIntervene.setStartDate(new Date());
                                healthIntervene.setEndDate(DateUtils.getFutureDate(new Date(), 90));
                                healthIntervene.setDelFlag("0");
                                healthIntervene.setPrompt(threadPrompt + "\n" + threadPromptTwo);
                                ICaszHealthInterveneService healthInterveneService = SpringUtils
                                        .getBean(ICaszHealthInterveneService.class);
                                healthInterveneService.save(healthIntervene);

                                CaszScreenForm screenForm = new CaszScreenForm();
                                screenForm.setCreateTime(new Date());
                                screenForm.setFormValue(call);
                                screenForm.setHosId(byId.getUnitId());
                                screenForm.setPatientId(byId.getPatientId());
                                screenForm.setScreeningId(healthIntervene.getId());
                                screenForm.setDelFlag("0");
                                screenForm.setDataSource("1");
                                screenForm.setIdNo(byId.getIdNo());
                                screenForm.setFormId(jzForm.getId());
                                screenForm.setFormText(jzForm.getFormDesignerText());
                                screenForm.setFormKey(jzForm.getFormKey());
                                screenForm.setFormName(jzForm.getFormName());

                                ICaszScreenFormService screenFormService = SpringUtils
                                        .getBean(ICaszScreenFormService.class);
                                screenFormService.save(screenForm);

                                // 设置进度为100%并自动删除
                                redisCache.setCacheObject(progressKey, "100", 5, java.util.concurrent.TimeUnit.SECONDS);

                                im_logger.info("schemeNew 执行成功，visitId: {}, 尝试次数: {}", visitId, currentAttempt + 1);
                                return null;
                            } catch (Exception e) {
                                im_logger.error("schemeNew 处理结果时发生错误，visitId: {}, 尝试次数: {}, 错误: {}", visitId,
                                        currentAttempt + 1, e.getMessage(), e);
                                throw new RuntimeException("结果处理失败: " + e.getMessage(), e);
                            }
                        });

                // 统一异常处理
                combinedFuture.exceptionally(ex -> {
                    // 如果出错，删除进度记录
                    redisCache.deleteObject(progressKey);

                    // 提取真实异常
                    Throwable cause = ex;
                    while (cause instanceof CompletionException && cause.getCause() != null) {
                        cause = cause.getCause();
                    }

                    im_logger.error("schemeNew 异步执行失败，visitId: {}, 尝试次数: {}, 错误: {}", visitId, currentAttempt + 1,
                            cause.getMessage(), cause);
                    handleSchemeNewRetry(visitId, otherRemarks, currentAttempt, maxRetries, cause);
                    return null;
                });

            }
        } catch (Exception e) {
            // 如果出错，删除进度记录
            System.out.println("3333333");
            redisCache.deleteObject(progressKey);
            im_logger.error("schemeNew 执行出错，visitId: {}, 尝试次数: {}, 错误: {}", visitId, currentAttempt + 1, e.getMessage(),
                    e);
            handleSchemeNewRetry(visitId, otherRemarks, currentAttempt, maxRetries, e);
        }
    }

    /**
     * 处理 schemeNew 重试逻辑
     */
    private static void handleSchemeNewRetry(String visitId, String otherRemarks, int currentAttempt, int maxRetries,
            Throwable error) {
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        String progressKey = "scheme_progress:" + visitId;

        if (currentAttempt < maxRetries - 1) {
            int nextAttempt = currentAttempt + 1;
            long delayMs = 2000L * (1L << nextAttempt); // 指数退避：2秒、4秒、8秒
            im_logger.warn("schemeNew 第{}次尝试失败，{}ms后进行第{}次重试，visitId: {}",
                    currentAttempt + 1, delayMs, nextAttempt + 1, visitId);

            // 设置重试等待进度
            redisCache.setCacheObject(progressKey, "分析失败，准备重试中", 300, java.util.concurrent.TimeUnit.MINUTES);

            Timer retryTimer = new Timer();
            retryTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    executeSchemeNewWithRetry(visitId, otherRemarks, nextAttempt, maxRetries);
                }
            }, delayMs);
        } else {
            // 最终失败，删除进度记录
            redisCache.deleteObject(progressKey);
            im_logger.error("schemeNew 重试{}次后仍然失败，终止执行，visitId: {}, 最终错误: {}",
                    maxRetries, visitId, error.getMessage(), error);
        }
    }

    public static TimerTask schemeSplit(String visitId, String otherRemarks) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    IFatVisitRecordService fatVisitRecordService = SpringUtils.getBean(IFatVisitRecordService.class);
                    FatVisitRecord byId = fatVisitRecordService.getById(visitId);
                    if (byId == null)
                        throw new RuntimeException("没有找到对应的就诊记录");
                    // 查询问卷
                    ICaszExamResultService caszExamResultService = SpringUtils.getBean(ICaszExamResultService.class);
                    LambdaQueryWrapper<CaszExamResult> caszExamResultLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    caszExamResultLambdaQueryWrapper.eq(CaszExamResult::getPatientId, byId.getPatientId());
                    caszExamResultLambdaQueryWrapper.orderByDesc(BaseEntity::getCreateTime);
                    caszExamResultLambdaQueryWrapper.eq(CaszExamResult::getFormId, "1898574044028858369");
                    caszExamResultLambdaQueryWrapper.last("limit 1");
                    CaszExamResult one1 = caszExamResultService.getOne(caszExamResultLambdaQueryWrapper);
                    ICaszDynamicFormService dynamicFormService = SpringUtils.getBean(ICaszDynamicFormService.class);
                    CaszDynamicForm jzForm = dynamicFormService.getById("1898574044028858369");
                    String idMap = "{}";
                    if (jzForm != null) {
                        idMap = jzForm.getIdMap();
                    }
                    String exam = FormTextValueUtils.transformJsonToChineseDisplay(one1.getFormValue(), idMap);
                    // 查询检测报告
                    StringBuffer labCheck = new StringBuffer();
                    IFatPictureService fatPictureService = SpringUtils.getBean(IFatPictureService.class);
                    LambdaQueryWrapper<FatPicture> pictureLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    pictureLambdaQueryWrapper.eq(FatPicture::getJzId, visitId);
                    List<FatPicture> list = fatPictureService.list(pictureLambdaQueryWrapper);
                    if (!list.isEmpty()) {
                        for (int i = 0; i < list.size(); i++) {
                            FatPicture fatPicture = list.get(i);
                            if (StringUtils.isNotEmpty(fatPicture.getCnText())) {
                                labCheck.append("\n检查报告").append(i + 1).append(":").append(fatPicture.getCnText());
                            }
                        }
                    }
                    IFatModelSetService fatModelSetService = SpringUtils.getBean(IFatModelSetService.class);
                    LambdaQueryWrapper<FatModelSet> modelSetLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    modelSetLambdaQueryWrapper.eq(FatModelSet::getUnitId, byId.getUnitId());
                    modelSetLambdaQueryWrapper.eq(FatModelSet::getPromptType, "schemeHealth");
                    modelSetLambdaQueryWrapper.last("limit 1");
                    FatModelSet one = fatModelSetService.getOne(modelSetLambdaQueryWrapper);

                    modelSetLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    modelSetLambdaQueryWrapper.eq(FatModelSet::getUnitId, byId.getUnitId());
                    modelSetLambdaQueryWrapper.eq(FatModelSet::getPromptType, "schemeEat");
                    modelSetLambdaQueryWrapper.last("limit 1");
                    FatModelSet two = fatModelSetService.getOne(modelSetLambdaQueryWrapper);

                    modelSetLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    modelSetLambdaQueryWrapper.eq(FatModelSet::getUnitId, byId.getUnitId());
                    modelSetLambdaQueryWrapper.eq(FatModelSet::getPromptType, "schemeSport");
                    modelSetLambdaQueryWrapper.last("limit 1");
                    FatModelSet three = fatModelSetService.getOne(modelSetLambdaQueryWrapper);

                    if (one != null) {
                        String prompt = one.getPrompt();
                        prompt += "\n请基于以下医患对话开展分析";
                        prompt += "\n门诊内容：" + byId.getAdvice();
                        if (StringUtils.isNotEmpty(exam)) {
                            prompt += "\n问卷内容：" + exam;
                        }
                        if (StringUtils.isNotEmpty(labCheck.toString())) {
                            prompt += labCheck.toString();
                        }

                        String promptTwo = two.getPrompt();
                        if (StringUtils.isNotEmpty(otherRemarks.toString())) {
                            promptTwo += otherRemarks.toString();
                        }

                        String promptThree = three.getPrompt();
                        if (StringUtils.isNotEmpty(otherRemarks.toString())) {
                            promptThree += otherRemarks.toString();
                        }

                        final String threadPrompt = prompt; // 提示词1
                        final String threadPromptTwo = promptTwo; // 提示词2
                        final String threadPromptThree = promptThree; // 提示词2
                        // 并行调用AI
                        CompletableFuture<String> future1 = CompletableFuture.supplyAsync(() -> {
                            try {
                                System.out.println("系统提示词" + one.getSysPrompt() + "主体提示词：" + threadPrompt);
                                return AIUtils.getThoughtAndContentByAgent(one.getSysPrompt(), threadPrompt);
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        });
                        CompletableFuture<String> future2 = CompletableFuture.supplyAsync(() -> {
                            try {
                                System.out.println("饮食提示词" + threadPromptTwo);
                                return AIUtils.getSchemeEat(threadPromptTwo);
                            } catch (NoApiKeyException e) {
                                throw new RuntimeException(e);
                            } catch (InputRequiredException e) {
                                throw new RuntimeException(e);
                            }
                        });

                        CompletableFuture<String> future3 = CompletableFuture.supplyAsync(() -> {
                            try {
                                System.out.println("运动提示词：" + threadPromptThree);
                                return AIUtils.getThoughtAndContentByAgent(three.getSysPrompt(), threadPromptThree);
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        });

                        CompletableFuture<Void> allFutures = CompletableFuture.allOf(future1, future2, future3);

                        allFutures.thenRun(() -> {

                            try {
                                String result1 = future1.get(); // 获取结果
                                String result2 = future2.get();
                                String result3 = future3.get();
                                Map<String, Object> replacements = new HashMap<>();
                                // 处理 result1 和 result2
                                String call = result1;
                                String thought = "";
                                String eatCall = result2;
                                String callSport = result3;
                                // String thoughtSport = result3.get("thought");
                                System.out.println("主体方案回复\n" + call);
                                System.out.println("饮食方案回复\n" + eatCall);
                                System.out.println("运动方案回复\n" + callSport);
                                if (StringUtils.isNotEmpty(call)) {
                                    call = call.replace("```json", "").replace("```", "");
                                    call = JsonSanitizer.sanitize(call);
                                }

                                // String eatCall = AIUtils.getSchemeEat(promptTwo);
                                if (StringUtils.isNotEmpty(eatCall)) {

                                    eatCall = eatCall.replace("```json", "").replace("```", "");
                                    JSONObject eatJson = JSONObject.parseObject(eatCall);

                                    // 2. 转换为 Map 方便操作
                                    Map<String, Object> map = eatJson.to(LinkedHashMap.class);
                                    replacements.put("个性化食谱参考", map.get(map.keySet().iterator().next())); // 把某个字段替换成
                                                                                                          // eatCall 的内容

                                }
                                if (StringUtils.isNotEmpty(callSport)) {
                                    callSport = callSport.replace("```json", "").replace("```", "");
                                    JSONObject sportJson = JSONObject.parseObject(callSport);
                                    Map<String, Object> sportMap = sportJson.to(LinkedHashMap.class);
                                    replacements.put("科学运动计划", sportMap.get(sportMap.keySet().iterator().next()));
                                }
                                call = JsonUtils.replaceJsonValue(call, replacements);
                                System.out.println("合并后的方案内容\n" + call);

                                CaszHealthIntervene healthIntervene = new CaszHealthIntervene();
                                healthIntervene.setCreateTime(new Date());
                                healthIntervene.setThought(thought);
                                healthIntervene.setPatientId(byId.getPatientId());
                                healthIntervene.setStatus("0");
                                healthIntervene.setHosId(byId.getUnitId());
                                healthIntervene.setDocterId(byId.getDoctorUserId());
                                healthIntervene.setDataSource("1");
                                healthIntervene.setAcceptStatus("Y");
                                healthIntervene.setStartDate(new Date());
                                healthIntervene.setEndDate(DateUtils.getFutureDate(new Date(), 90));
                                healthIntervene.setDelFlag("0");
                                healthIntervene.setPrompt(threadPrompt + "\n" + threadPromptTwo);
                                ICaszHealthInterveneService healthInterveneService = SpringUtils
                                        .getBean(ICaszHealthInterveneService.class);
                                healthInterveneService.save(healthIntervene);

                                CaszScreenForm screenForm = new CaszScreenForm();
                                screenForm.setCreateTime(new Date());
                                screenForm.setFormValue(call);
                                screenForm.setHosId(byId.getUnitId());
                                screenForm.setPatientId(byId.getPatientId());
                                screenForm.setScreeningId(healthIntervene.getId());
                                screenForm.setDelFlag("0");
                                screenForm.setDataSource("1");
                                screenForm.setIdNo(byId.getIdNo());
                                screenForm.setFormId(jzForm.getId());
                                screenForm.setFormText(jzForm.getFormDesignerText());
                                screenForm.setFormKey(jzForm.getFormKey());
                                screenForm.setFormName(jzForm.getFormName());

                                ICaszScreenFormService screenFormService = SpringUtils
                                        .getBean(ICaszScreenFormService.class);
                                screenFormService.save(screenForm);
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                            // 后续处理逻辑
                        });

                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException(e);
                }
            }
        };

    }

    public static TimerTask generateNewScheme(String visitId, String otherRemarks) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    executeGenerateNewSchemeWithRetry(visitId, otherRemarks, 0, 3);
                } catch (Exception e) {
                    // TimerTask中的异常必须在这里处理，否则会被线程池吞掉
                    im_logger.error("generateNewScheme TimerTask 执行失败，visitId: {}, 错误: {}", visitId, e.getMessage(), e);

                    // 删除进度记录
                    try {
                        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
                        String progressKey = "scheme_progress:" + visitId;
                        redisCache.deleteObject(progressKey);
                    } catch (Exception redisEx) {
                        im_logger.error("删除进度记录失败，visitId: {}, 错误: {}", visitId, redisEx.getMessage(), redisEx);
                    }

                    // 由于在TimerTask中，异常无法向外传播，所以记录最终失败
                    im_logger.error("generateNewScheme 最终失败，不再重试，visitId: {}", visitId);
                }
            }
        };
    }

    /**
     * 带重试机制的 generateNewScheme 执行方法
     *
     * @param visitId        访问ID
     * @param otherRemarks   其他备注
     * @param currentAttempt 当前尝试次数
     * @param maxRetries     最大重试次数
     */
    private static void executeGenerateNewSchemeWithRetry(String visitId, String otherRemarks, int currentAttempt,
            int maxRetries) {
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        String progressKey = "scheme_progress:" + visitId;
        try {
            // 设置初始进度
            redisCache.setCacheObject(progressKey, "正在准备干预方案", 300, java.util.concurrent.TimeUnit.MINUTES);

            im_logger.info("generateNewScheme 开始执行，visitId: {}, 当前尝试次数: {}/{}", visitId, currentAttempt + 1,
                    maxRetries);
            IFatVisitRecordService fatVisitRecordService = SpringUtils.getBean(IFatVisitRecordService.class);
            FatVisitRecord byId = fatVisitRecordService.getById(visitId);
            if (byId == null) {
                throw new RuntimeException("没有找到对应的就诊记录");
            }

            // 设置进度：准备数据
            redisCache.setCacheObject(progressKey, "正在收集患者信息", 300, java.util.concurrent.TimeUnit.MINUTES);
            // 查询问卷
            ICaszExamResultService caszExamResultService = SpringUtils.getBean(ICaszExamResultService.class);
            LambdaQueryWrapper<CaszExamResult> caszExamResultLambdaQueryWrapper = new LambdaQueryWrapper<>();
            caszExamResultLambdaQueryWrapper.eq(CaszExamResult::getPatientId, byId.getPatientId());
            caszExamResultLambdaQueryWrapper.orderByDesc(BaseEntity::getCreateTime);
            caszExamResultLambdaQueryWrapper.eq(CaszExamResult::getFormId, "1898574044028858369");
            caszExamResultLambdaQueryWrapper.last("limit 1");
            CaszExamResult one1 = caszExamResultService.getOne(caszExamResultLambdaQueryWrapper);
            ICaszDynamicFormService dynamicFormService = SpringUtils.getBean(ICaszDynamicFormService.class);
            CaszDynamicForm jzForm = dynamicFormService.getById("1898574044028858369");
            String idMap = "{}";
            if (jzForm != null) {
                idMap = jzForm.getIdMap();
            }
            String exam = FormTextValueUtils.transformJsonToChineseDisplay(one1.getFormValue(), idMap);
            // 查询检测报告
            StringBuffer labCheck = new StringBuffer();
            IFatPictureService fatPictureService = SpringUtils.getBean(IFatPictureService.class);
            LambdaQueryWrapper<FatPicture> pictureLambdaQueryWrapper = new LambdaQueryWrapper<>();
            pictureLambdaQueryWrapper.eq(FatPicture::getJzId, visitId);
            List<FatPicture> list = fatPictureService.list(pictureLambdaQueryWrapper);
            if (!list.isEmpty()) {
                for (int i = 0; i < list.size(); i++) {
                    FatPicture fatPicture = list.get(i);
                    if (StringUtils.isNotEmpty(fatPicture.getCnText())) {
                        labCheck.append("\n检查报告").append(i + 1).append(":").append(fatPicture.getCnText());
                    }
                }
            }
            IFatModelSetService fatModelSetService = SpringUtils.getBean(IFatModelSetService.class);
            LambdaQueryWrapper<FatModelSet> modelSetLambdaQueryWrapper = new LambdaQueryWrapper<>();
            modelSetLambdaQueryWrapper.eq(FatModelSet::getUnitId, byId.getUnitId());
            modelSetLambdaQueryWrapper.eq(FatModelSet::getPromptType, "schemeHealth");
            modelSetLambdaQueryWrapper.last("limit 1");
            FatModelSet one = fatModelSetService.getOne(modelSetLambdaQueryWrapper);

            modelSetLambdaQueryWrapper = new LambdaQueryWrapper<>();
            modelSetLambdaQueryWrapper.eq(FatModelSet::getUnitId, byId.getUnitId());
            modelSetLambdaQueryWrapper.eq(FatModelSet::getPromptType, "schemeHealthNew");
            modelSetLambdaQueryWrapper.last("limit 1");
            FatModelSet two = fatModelSetService.getOne(modelSetLambdaQueryWrapper);

            modelSetLambdaQueryWrapper = new LambdaQueryWrapper<>();
            modelSetLambdaQueryWrapper.eq(FatModelSet::getUnitId, byId.getUnitId());
            modelSetLambdaQueryWrapper.eq(FatModelSet::getPromptType, "schemeEatNew");
            modelSetLambdaQueryWrapper.last("limit 1");
            FatModelSet three = fatModelSetService.getOne(modelSetLambdaQueryWrapper);
            if (one != null) {
                // 设置进度：构建提示词
                redisCache.setCacheObject(progressKey, "正在构建分析提示词", 300, java.util.concurrent.TimeUnit.MINUTES);

                String prompt = one.getPrompt();
                prompt += "\n请基于以下医患对话开展分析";
                prompt += "\n门诊内容：" + byId.getAdvice();
                if (StringUtils.isNotEmpty(exam)) {
                    prompt += "\n问卷内容：" + exam;
                }
                if (StringUtils.isNotEmpty(labCheck.toString())) {
                    prompt += labCheck.toString();
                }

                String promptTwo = two.getPrompt();
                promptTwo += "\n请基于以下医患对话开展分析";
                promptTwo += "\n门诊内容：" + byId.getAdvice();
                if (StringUtils.isNotEmpty(exam)) {
                    promptTwo += "\n问卷内容：" + exam;
                }
                if (StringUtils.isNotEmpty(labCheck.toString())) {
                    promptTwo += labCheck.toString();
                }
                if (StringUtils.isNotEmpty(otherRemarks)) {
                    promptTwo += "\n其他备注：" + otherRemarks;
                }

                String promptThree = three.getPrompt();
                if (StringUtils.isNotEmpty(otherRemarks.toString())) {
                    promptThree += otherRemarks.toString();
                }

                final String threadPrompt = prompt; // 提示词1
                final String threadPromptTwo = promptTwo; // 提示词2
                final String threadPromptThree = promptThree; // 提示词3

                // 设置进度：开始AI分析
                redisCache.setCacheObject(progressKey, "正在全力分析干预方案", 300, java.util.concurrent.TimeUnit.MINUTES);

                // 并行调用AI - 修复异常处理
                CompletableFuture<Map<String, String>> future1 = CompletableFuture.supplyAsync(() -> {
                    try {
                        System.out.println("系统提示词" + one.getSysPrompt() + "主体提示词：" + threadPrompt);
                        return AIUtils.getThoughtAndContent(one.getSysPrompt(), threadPrompt);
                    } catch (Exception e) {
                        im_logger.error("AI调用失败 - future1, visitId: {}, 错误: {}", visitId, e.getMessage(), e);
                        throw new RuntimeException("AI分析失败: " + e.getMessage(), e);
                    }
                });

                CompletableFuture<String> future2 = CompletableFuture.supplyAsync(() -> {
                    try {
                        System.out.println("健康提示词" + threadPromptTwo);
                        return AIUtils.getThoughtAndContentByAgent(two.getSysPrompt(), threadPromptTwo);
                    } catch (Exception e) {
                        im_logger.error("AI调用失败 - future2, visitId: {}, 错误: {}", visitId, e.getMessage(), e);
                        throw new RuntimeException("健康方案生成失败: " + e.getMessage(), e);
                    }
                });

                CompletableFuture<String> future3 = CompletableFuture.supplyAsync(() -> {
                    try {
                        System.out.println("饮食提示词" + threadPromptThree);
                        return AIUtils.getSchemeEat(threadPromptThree);
                    } catch (Exception e) {
                        im_logger.error("AI调用失败 - future3, visitId: {}, 错误: {}", visitId, e.getMessage(), e);
                        throw new RuntimeException("饮食方案生成失败: " + e.getMessage(), e);
                    }
                });

                CompletableFuture<Void> allFutures = CompletableFuture.allOf(future1, future2, future3);

                // 统一的异常处理
                CompletableFuture<Void> combinedFuture = allFutures.thenRun(() -> {
                    try {
                        String result1 = future1.get().get("content"); // 获取结果
                        String thought1 = future1.get().get("thought");
                        String result2 = future2.get();
                        String result3 = future3.get();
                        Map<String, Object> replacements = new HashMap<>();

                        // 设置进度：整理分析结果
                        redisCache.setCacheObject(progressKey, "正在整理干预方案结果", 300,
                                java.util.concurrent.TimeUnit.MINUTES);

                        // 处理结果
                        String call = result1;
                        String thought = thought1;
                        String healthCall = result2;
                        String eatCall = result3;
                        System.out.println("主体方案回复\n" + call);
                        System.out.println("健康方案回复\n" + healthCall);
                        System.out.println("饮食方案回复\n" + eatCall);
                        if (StringUtils.isNotEmpty(call)) {
                            call = call.replace("```json", "").replace("```", "");
                            call = JsonSanitizer.sanitize(call);
                        }

                        if (StringUtils.isNotEmpty(healthCall)) {
                            healthCall = healthCall.replace("```json", "").replace("```", "");
                            JSONObject healthJson = JSONObject.parseObject(healthCall);
                            Map<String, Object> healthMap = healthJson.to(LinkedHashMap.class);
                            // 根据需要替换相应字段
                            replacements.put("健康管理方案", healthMap.get(healthMap.keySet().iterator().next()));
                        }

                        if (StringUtils.isNotEmpty(eatCall)) {
                            eatCall = eatCall.replace("```json", "").replace("```", "");
                            JSONObject eatJson = JSONObject.parseObject(eatCall);
                            // 2. 转换为 Map 方便操作
                            Map<String, Object> map = eatJson.to(LinkedHashMap.class);
                            replacements.put("个性化饮食食谱", map.get(map.keySet().iterator().next()));
                        }

                        call = JsonUtils.replaceJsonValue(call, replacements);
                        System.out.println("合并后的方案内容\n" + call);

                        CaszHealthIntervene healthIntervene = new CaszHealthIntervene();
                        healthIntervene.setCreateTime(new Date());
                        healthIntervene.setThought(thought);
                        healthIntervene.setDataSource("2");
                        healthIntervene.setPatientId(byId.getPatientId());
                        healthIntervene.setStatus("0");
                        healthIntervene.setHosId(byId.getUnitId());
                        healthIntervene.setDocterId(byId.getDoctorUserId());
                        healthIntervene.setAcceptStatus("Y");
                        healthIntervene.setStartDate(new Date());
                        healthIntervene.setEndDate(DateUtils.getFutureDate(new Date(), 90));
                        healthIntervene.setDelFlag("0");
                        healthIntervene.setPrompt(threadPrompt + "\n" + threadPromptTwo + "\n" + threadPromptThree);
                        ICaszHealthInterveneService healthInterveneService = SpringUtils
                                .getBean(ICaszHealthInterveneService.class);
                        healthInterveneService.save(healthIntervene);

                        CaszScreenForm screenForm = new CaszScreenForm();
                        screenForm.setCreateTime(new Date());
                        screenForm.setFormValue(call);
                        screenForm.setDataSource("2");
                        screenForm.setHosId(byId.getUnitId());
                        screenForm.setPatientId(byId.getPatientId());
                        screenForm.setScreeningId(healthIntervene.getId());
                        screenForm.setDelFlag("0");
                        screenForm.setIdNo(byId.getIdNo());
                        screenForm.setFormId(jzForm.getId());
                        screenForm.setFormText(jzForm.getFormDesignerText());
                        screenForm.setFormKey(jzForm.getFormKey());
                        screenForm.setFormName(jzForm.getFormName());

                        ICaszScreenFormService screenFormService = SpringUtils
                                .getBean(ICaszScreenFormService.class);
                        screenFormService.save(screenForm);

                        // 设置进度为100%并自动删除
                        redisCache.setCacheObject(progressKey, "100", 5, java.util.concurrent.TimeUnit.SECONDS);

                        im_logger.info("generateNewScheme 执行成功，visitId: {}, 尝试次数: {}", visitId, currentAttempt + 1);
                    } catch (Exception e) {
                        im_logger.error("generateNewScheme 处理结果时发生错误，visitId: {}, 尝试次数: {}, 错误: {}", visitId,
                                currentAttempt + 1, e.getMessage(), e);
                        throw new RuntimeException("结果处理失败: " + e.getMessage(), e);
                    }
                });

                // 统一异常处理
                combinedFuture.exceptionally(ex -> {
                    // 如果出错，删除进度记录
                    redisCache.deleteObject(progressKey);

                    // 提取真实异常
                    Throwable cause = ex;
                    while (cause instanceof CompletionException && cause.getCause() != null) {
                        cause = cause.getCause();
                    }

                    im_logger.error("generateNewScheme 异步执行失败，visitId: {}, 尝试次数: {}, 错误: {}", visitId,
                            currentAttempt + 1,
                            cause.getMessage(), cause);
                    handleGenerateNewSchemeRetry(visitId, otherRemarks, currentAttempt, maxRetries, cause);
                    return null;
                });

            }
        } catch (Exception e) {
            // 如果出错，删除进度记录
            redisCache.deleteObject(progressKey);
            im_logger.error("generateNewScheme 执行出错，visitId: {}, 尝试次数: {}, 错误: {}", visitId, currentAttempt + 1,
                    e.getMessage(), e);
            handleGenerateNewSchemeRetry(visitId, otherRemarks, currentAttempt, maxRetries, e);
        }
    }

    /**
     * 处理 generateNewScheme 重试逻辑
     */
    private static void handleGenerateNewSchemeRetry(String visitId, String otherRemarks, int currentAttempt,
            int maxRetries, Throwable error) {
        RedisCache redisCache = SpringUtils.getBean(RedisCache.class);
        String progressKey = "scheme_progress:" + visitId;

        if (currentAttempt < maxRetries - 1) {
            int nextAttempt = currentAttempt + 1;
            long delayMs = 2000L * (1L << nextAttempt); // 指数退避：2秒、4秒、8秒
            im_logger.warn("generateNewScheme 第{}次尝试失败，{}ms后进行第{}次重试，visitId: {}",
                    currentAttempt + 1, delayMs, nextAttempt + 1, visitId);

            // 设置重试等待进度
            redisCache.setCacheObject(progressKey, "分析失败，准备重试中", 300, java.util.concurrent.TimeUnit.MINUTES);

            Timer retryTimer = new Timer();
            retryTimer.schedule(new TimerTask() {
                @Override
                public void run() {
                    executeGenerateNewSchemeWithRetry(visitId, otherRemarks, nextAttempt, maxRetries);
                }
            }, delayMs);
        } else {
            // 最终失败，删除进度记录
            redisCache.deleteObject(progressKey);
            im_logger.error("generateNewScheme 重试{}次后仍然失败，终止执行，visitId: {}, 最终错误: {}",
                    maxRetries, visitId, error.getMessage(), error);
        }
    }

    public static void main(String[] args) throws Exception {
        String idNo = "123456789012";
        String lastSixDigits = StringUtils.substring(idNo, -6);
        System.out.println(lastSixDigits); // 输出: 567890

        // Timer timer = new Timer();
        // timer.schedule(adviceSplit("1925457621192540161", ""), 0);
        //
        // // 可选：保持主线程存活一段时间以观察异步结果
        // Thread.sleep(50000); // 等待最多10秒让异步任务完成
        //
        // System.out.println("Main thread exiting.");

    }

}

<template>
	<view class="container">
		<Navbar ref="navbar" :hideBtn="true" leftIcon="arrow-left"
			:titleIcon="isHosAiEffect?titleIcon:''" :title="conversation.userInfo.nickname?conversation.userInfo.nickname:conversation.groupInfo.name"
			@titleClick="titleClick" @leftClick="navLeftClick" @dblclick="forceResetState" bgColor="#b7bbfa" :h5Show="false" :fixed="true" :titleStyle="{color:'#FFFFFF'}">
		</Navbar>


		<uni-list :border="false" @tap="hideOtherPanel" :class="{'list-panel-open': isFaceViewShow || isMoreViewShow}">
			<uni-list-item :border="false" v-for="item in messageList" :key="item.messageId">
				<template v-slot:body>
					<view style="width: 100%;">
						<view class="record-item" :class="(user.userId == item.from) ? 'row-right' : ''"
							v-if="item.type != 'group_sys_notice' && item.type != 'sys_notice'">
							<image class="avatar" :src="item.fromUserInfo.avatarUrl" mode="aspectFill"></image>
							<view class="right" :class="(user.userId == item.from) ? 'right-align' : ''">
								<view style="display: flex;flex-direction: row;"
									:style="{'justifyContent':(user.userId == item.from) ? 'flex-end' : ''}">
									<view class="nickname"
										v-if="conversation.type == 'group' && item.from != user.userId"
										:class="(user.userId == item.from) ? 'nickname-right' : ''">
										<text>{{item.fromUserInfo.nickname}}</text>
									</view>
									<view class="nickname">
										<text>{{msgTime(item.time)}}</text>
									</view>
								</view>
								<view class="message-box">
									<view class="text-box" :class="(user.userId == item.from) ? 'text-bg-right' : ''"
										v-if="item.type == 'text'">
										<mp-html :content="parseText(item.body.text)" />
									</view>
									<view class="image-box" v-else-if="item.type == 'image'" @click="previewImage(item)">
										<image :src="item.body.thumbnailUrl" mode="aspectFill"
											@error="onImageError" @load="onImageLoad"
											:show-loading="true" :lazy-load="true"></image>
									</view>
									<view class="audio-box" :class="(user.userId == item.from) ? 'audio-bg-right' : ''"
										v-else-if="item.type == 'audio'" @click="playAudio(item)">
										<image
											src="https://mobile-**********.cos.ap-guangzhou.myqcloud.com/yeim/voice_static.png"
											mode="aspectFill"></image>
										<text>{{item.body.duration}}'</text>
									</view>
									<view class="video-box" v-else-if="item.type == 'video'">
										<video :src="item.body.videoUrl"></video>
									</view>
									<view class="location-box" v-else-if="item.type == 'location'">
										<view class="location-header">
											<text>{{item.body.address}}</text>
											<text>{{item.body.description}}</text>
										</view>
										<view class="location-map">
											<image
												:src="'https://apis.map.qq.com/ws/staticmap/v2/?center=' + item.body.latitude + ',' + item.body.longitude + '&zoom=15&size=450*200&maptype=roadmap&markers=size:large|color:0xFFCCFF|label:k|' + item.body.latitude + ',' + item.body.longitude + '&key=DABBZ-3OYKU-FORVK-42TKO-3COV5-V5BQB'"
												mode="aspectFit"></image>
										</view>
									</view>
								</view>
							</view>
						</view>
						<view class="record-sys-tips-item" v-else>
							<text>{{item.body.tips}}</text>
						</view>
					</view>

				</template>
			</uni-list-item>
		</uni-list>
		<view class="footer" :style="footerBottomStyle" :class="{'footer-safe-area': true}">
			<view class="voice" @click="handleVoiceClick" :class="{'disabled': isAiReplying}">
				<image :src="voiceIcon" mode="widthFix"></image>
			</view>
			<view class="input" v-if="!isVoiceInput">
				<input @click="hideOtherPanel" :adjust-position="true" cursor-spacing="20" confirm-type="send"
					v-model="inputText" @confirm="sendTextMessage" class="uni-input"
					:placeholder="isAiReplying ? 'AI正在回复中，请稍候...' : '说点什么吧~'" :disabled="isAiReplying" />
			</view>
			<view class="voice-input" @touchstart="handleVoiceBegin"
				@touchmove.stop.prevent="handleVoiceIng" @touchend="handleVoiceEnd"
				@touchcancel="handleVoiceCancel" v-else :class="{'disabled': isAiReplying}">
				<text>{{ isAiReplying ? 'AI正在回复中...' : '按住说话' }}</text>
			</view>
			<view class="face" @click="handleFaceClick" :class="{'disabled': isAiReplying}" @tap="handleFaceClick">
				<image src="https://mobile-**********.cos.ap-guangzhou.myqcloud.com/yeim/ic_footer_face.png"
					mode="widthFix"></image>
			</view>
			<view v-if="inputText && !isAiReplying" class="more" @click="sendTextMessage">
				<button class="mini-btn" style="background-color: #b7bbfa;color:#fff" size="mini">发送</button>
			</view>
			<view v-else-if="!isAiReplying" class="more" @click="more" @tap="more">
				<image src="https://mobile-**********.cos.ap-guangzhou.myqcloud.com/yeim/ic_footer_more.png"
					mode="widthFix"></image>
			</view>
			<view v-else class="more disabled">
				<image src="https://mobile-**********.cos.ap-guangzhou.myqcloud.com/yeim/ic_footer_more.png"
					mode="widthFix"></image>
			</view>
		</view>

		<view class="record" v-if="recording">
			<view class="gif">
				<image src="https://mobile-**********.cos.ap-guangzhou.myqcloud.com/yeim/recording.gif"
					class="recording" mode="aspectFit"></image>
			</view>
			<view class="recordTis"><text class="recordTis-text">{{recordTis}}</text></view>
		</view>

		<!-- 面板遮罩层 -->
		<view v-if="isFaceViewShow || isMoreViewShow" class="panel-mask" @click="hideOtherPanel"></view>

		<view class="emoji-view" :class="{'emoji-safe-area': true, 'emoji-show': isFaceViewShow}">
			<scroll-view scroll-y="true" class="scroll-Y" style="height: 600rpx;">
				<view v-for="i in 14" :key="i" style="display: flex;justify-content: center;">
					<view v-for="j in 8" :key="j" style="padding: 10rpx 0;flex:auto;text-align: center;">
						<image @click="emojiClick(((j+1) + ((i+1) - 1) * 8))" style="width: 60rpx;height: 60rpx"
							:src="'https://mobile-**********.cos.ap-guangzhou.myqcloud.com/yeim/face/Expression_'+(((j+1) + ((i+1) - 1) * 8))+'@2x.png'"
							mode=""></image>
					</view>
				</view>
			</scroll-view>
		</view>

		<view class="more-view" :class="{'more-safe-area': true, 'more-show': isMoreViewShow}">
			<view class="more-tool" @click="chooseImage">
				<view class="more-icon">
					<image src="https://mobile-**********.cos.ap-guangzhou.myqcloud.com/yeim/ic_more_item_image.png"
						mode=""></image>
				</view>
				<view class="more-text">
					相册
				</view>
			</view>
			<view class="more-tool" @click="capImage">
				<view class="more-icon">
					<image src="https://mobile-**********.cos.ap-guangzhou.myqcloud.com/yeim/ic_more_item_cap.png"
						mode=""></image>
				</view>
				<view class="more-text">
					拍照
				</view>
			</view>
			<view class="more-tool" @click="chooseVideo">
				<view class="more-icon">
					<image src="https://mobile-**********.cos.ap-guangzhou.myqcloud.com/yeim/ic_more_item_video.png"
						mode=""></image>
				</view>
				<view class="more-text">
					视频
				</view>
			</view>
			<!-- <view class="more-tool">
				<view class="more-icon">
					<image src="https://mobile-**********.cos.ap-guangzhou.myqcloud.com/yeim/ic_more_item_files.png" mode=""></image>
				</view>
				<view class="more-text">
					文件
				</view>
			</view> -->
			<!-- <view class="more-tool" @click="chooseLocation">
				<view class="more-icon">
					<image src="https://mobile-**********.cos.ap-guangzhou.myqcloud.com/yeim/ic_more_item_location.png" mode=""></image>
				</view>
				<view class="more-text">
					位置
				</view>
			</view> -->

		</view>
		<u-action-sheet :show="showAiRoleSel" :actions="aiRoleActions" title="AI人设" description="请选择想要对话的AI人设"
			@close="showAiRoleSel = false" @select="aiRoleSelect">
		</u-action-sheet>
	</view>
</template>

<script>
	//录音API
	const recorderManager = uni.getRecorderManager();
	import * as UnitApi from "@/api/work/unit.js"
	import * as AiRoleApi from "@/api/aiRole.js"
	import Navbar from '@/components/navbar/Navbar'
	import {
		mapState
	} from 'vuex'
	import {
		YeIMUniSDK,
		YeIMUniSDKDefines
	} from '../../uni_modules/wzJun1-YeIM-Uni-SDK/js_sdk/yeim-uni-sdk.min';
	import {
		formatInputHtml,
		getPurePath,
		html2Text,
		getPicInfo,
		getFileType,
		getVideoSnshot,
		base64toFile,
		uploadForm,
		getFileSize
	} from "@/utils/common";
	import {
		msgDateTimeFormat
	} from '../../utils';
	export default {
		components: {
			Navbar
		},
		data() {
			return {
				//临时会话
				conversation: {
					id: undefined,
					name: undefined
				},
				nextMessageId: '',
				//消息数据列表
				messageList: [],
				//输入框内容
				inputText: "",
				//是否语音输入
				isVoiceInput: false,
				//语音、键盘图标
				voiceIcon: 'https://mobile-**********.cos.ap-guangzhou.myqcloud.com/yeim/ic_footer_voice.png',
				//语音输入UI变量
				voiceTis: '按住 说话',
				recordTis: "手指上滑 取消发送",
				recording: false,
				recordWillStop: false,
				initPoint: {
					identifier: 0,
					Y: 0
				},
				recordTimer: null,
				recordLength: 0,
				//显示表情面板
				isFaceViewShow: false,
				//显示更多面板
				isMoreViewShow: false,
				//是否开启ai
				isHosAiEffect: false,
				//是否ai回复等待中
				isAiReplying: false,
				unitId: '',
				patientId: '',
				sysUserId: '',
				titleIcon: 'https://fat-**********.cos.ap-nanjing.myqcloud.com/weixin/down.png',
				showAiRoleSel: false,
				aiRoleActions: [],
				roleTips: '',
				audioContext: null, // 音频播放实例
				playingAudioId: null, // 当前播放的消息ID
				isAudioPlaying: false, // 播放状态
				lastFaceClickTime: 0, // 上次点击表情按钮的时间，用于防抖
				systemInfo: null, // 系统信息
				safeAreaInsets: { // 安全区域信息
					bottom: 0
				}
			};
		},
		computed: {
			...mapState({
				user: state => state.user
			}),
			// 计算footer的底部位置，考虑安全区域
			footerBottomStyle() {
				if (this.isFaceViewShow || this.isMoreViewShow) {
					return 'bottom: 600rpx';
				}
				return 'bottom: 0px';
			}
		},
		onLoad(options) {

			// 获取系统信息，用于安全区域适配
			this.getSystemInfo();

			this.unitId = this.$store.state.unit.id;
			this.patientId = this.$store.state.unit.unionId;
			this.sysUserId = this.$store.state.userInfo.user.userId
			let app = this;
			//获取会话详情
			let result = YeIMUniSDK.getInstance().getConversation(options.conversationId);
			console.log("获取会话详情", result)
			if (result.message) {
				app.conversation = result.message;
			} else {
				app.conversation = {
					conversationId: options.conversationId,
					type: options.conversationType,
					userInfo: options.conversationType == YeIMUniSDKDefines.CONVERSATION_TYPE.PRIVATE ? {
						nickname: options.nickname
					} : {},
					groupInfo: {
						name: options.conversationType == YeIMUniSDKDefines.CONVERSATION_TYPE.PRIVATE ? {
							name: options.nickname
						} : {}
					}
				}
			}
			if (app.conversation.type == YeIMUniSDKDefines.CONVERSATION_TYPE.PRIVATE) {
				uni.setNavigationBarTitle({
					title: app.conversation.userInfo.nickname
				})
				//私聊--查询是否是医疗机构聊天
				app.getUnitAiData()
			} else {
				uni.setNavigationBarTitle({
					title: app.conversation.groupInfo.name
				})
			}

			recorderManager.onStart((e) => {
				console.log("录音管理器 onStart 回调", e);
				app.recordBegin(e);
			})
			recorderManager.onStop((e) => {
				console.log("录音管理器 onStop 回调", e);
				app.recordEnd(e);
			})
			recorderManager.onError((e) => {
				console.error("录音管理器 onError 回调", e);
				app.recording = false;
				uni.showToast({
					title: '录音失败: ' + (e.errMsg || '未知错误'),
					icon: 'none'
				});
			})
			//进入页面加载消息记录
			app.getHistoryMessageList();
			//清除会话未读数
			YeIMUniSDK.getInstance().clearConversationUnread(app.conversation.conversationId);

			// 检查是否有语音消息参数
			if (options.voiceText) {
				console.log('检测到语音消息参数:', options.voiceText);
				// 延迟一段时间后发送语音转文字消息
				setTimeout(() => {
					console.log('自动发送语音转文字消息:', options.voiceText);
					// 发送文字消息
					app.sendVoiceTextMessage(options.voiceText);
				}, 800);
			} else {
				// 检查本地存储中是否有语音消息
				try {
					const lastVoiceMessage = uni.getStorageSync('last_voice_message');
					if (lastVoiceMessage && lastVoiceMessage.text && lastVoiceMessage.timestamp &&
						(Date.now() - lastVoiceMessage.timestamp < 30000)) { // 30秒内的消息才有效
						console.log('从本地存储获取到语音消息:', lastVoiceMessage.text);
						// 延迟发送语音转文字消息
						setTimeout(() => {
							this.sendVoiceTextMessage(lastVoiceMessage.text);
						}, 800);
					}
				} catch (e) {
					console.error('获取本地语音消息失败:', e);
				}
			}
		},
		onShow() {
			// 重置可能卡住的状态
			if (this.isAiReplying) {
				this.isAiReplying = false;
				uni.hideLoading();
			}

			if (this.recording) {
				this.recording = false;
				this.voiceTis = '按住 说话';
				this.recordTis = '手指上滑 取消发送';
			}

			// 重置表情面板和更多面板状态，防止意外弹出
			this.isFaceViewShow = false;
			this.isMoreViewShow = false;

			//监听新消息
			YeIMUniSDK.getInstance().addEventListener(YeIMUniSDKDefines.EVENT.MESSAGE_RECEIVED, this.onMessage);
		},
		onHide() {
			YeIMUniSDK.getInstance().removeEventListener(YeIMUniSDKDefines.EVENT.MESSAGE_RECEIVED, this.onMessage);
			//清除指定会话未读数，并给对方发送已读回执
			YeIMUniSDK.getInstance().clearConversationUnread(this.conversation.conversationId);
		},
		onPullDownRefresh() {
			this.getHistoryMessageList();
		},
		methods: {
			clickBtn() {
				//conversationType 1 为单聊，3 为群聊
				if (!this.conversation) {
					uni.$u.toast("获取当前会话信息错误")
					return;
				}
				console.log("currentConversation", this.conversation)
				if (this.conversation && this.conversation.type == 'private') {
					uni.navigateTo({
						url: '/pageWork/patientReport/patientReport?userID=' + this.conversation.conversationId
					})
				}
			},
			msgTime(time) {
				return msgDateTimeFormat(time)
			},
			//选择ai角色后
			aiRoleSelect(e) {
				console.log(e)
				AiRoleApi
					.addOrUpdateUserAiRole({
						sysUserId: this.sysUserId,
						aiRoleId: e.id,
						roleTips: e.roleTips,
						roleName: e.roleName,
						imUserId: this.user.userId
					}).then(res => {
						if (res.code == 200) {
							this.getAiRoles()
							uni.$u.toast("设置成功")
						}
					})
			},
			//查询ai角色
			titleClick() {
				if (this.isHosAiEffect) {
					this.showAiRoleSel = true
				} else if (this.conversation.type == 'group') {
					uni.navigateTo({
						url: '/pageYeChat/chat/groupInfo?groupId=' + this.conversation.conversationId
					})
				}

			},
			navLeftClick() {
				uni.navigateBack()
			},
			getUnitAiData() {
				let query = {
					userId: this.conversation.conversationId
				}
				UnitApi.unitAiStatus(query).then(res => {
					console.log("机构的AI小助手开启状态", res)
					if (res.code == 200) {
						this.isHosAiEffect = res.data.aiOpen
						if (this.isHosAiEffect) {
							this.getAiRoles()
						}
					}
				})
			},
			getAiRoles() {
				AiRoleApi.getAiRoles().then(res => {
					if (res.code == 200) {
						this.aiRoleActions = [{
							name: '清除AI角色',
							id: '-1'
						}]
						res.data.forEach(item => {
							item.name = item.roleName
							if (item.params && item.params.isCheck) {
								item.color = '#b7bbfa' //high light current ai role
								this.roleTips = item.roleTips
							}
							this.aiRoleActions.push(item)
						})
					}
				})
			},
			onMessage(e) {
				console.log("收到新消息")
				let message = e;
				this.insertMessage(message);
			},
			insertMessage(message) {
				console.log("发送消息之后，插入消息", message)
				try {
					console.log(message.conversationId)
					console.log(this.conversation.conversationId)
					if (message.conversationId === this.conversation.conversationId) {
						let index = this.messageList.findIndex(item => item.messageId === message.messageId)
						console.log(index)
						if (index === -1) {
							this.messageList.push(message);
							this.pageScrollToBottom();
						}
					}

					//请求ai回复  开启ai并且是用户发出
					if (this.isHosAiEffect && message.from == this.user.userId) {
						this.isAiReplying = true
						uni.showLoading({
							title: '『努力思考中...』',
							mask: true
						})
						message.body.unitId = this.unitId
						message.body.patientId = this.patientId
						message.body.roleTips = this.roleTips
						UnitApi.yeAiReply(message).then(res => {
							console.log("ai回复结果", res)
							this.isAiReplying = false
							uni.hideLoading()
						}).catch(err => {
							console.error("ai回复失败", err)
							this.isAiReplying = false
							uni.hideLoading()
							uni.showToast({
								title: 'AI回复失败，请重试',
								icon: 'none'
							})
						})
					}

				} catch (e) {
					console.log(e)
				}
			},
			getHistoryMessageList() {
				console.log("getHistoryMessageList-nextMessageId", this.nextMessageId);
				YeIMUniSDK.getInstance().getHistoryMessageList({
					nextMessageId: this.nextMessageId,
					conversationId: this.conversation.conversationId,
					success: (res) => {
						console.log("getHistoryMessageList", res);
						if (this.messageList.length <= 0) {
							this.messageList = res.data.list;
							this.pageScrollToBottom(0);
						} else {
							this.messageList.unshift(...res.data.list);
						}

						if (res.data.nextMessageId) {
							this.nextMessageId = res.data.nextMessageId;
							console.log(this.nextMessageId)
						}

						uni.stopPullDownRefresh();
					},
					fail: (err) => {
						console.log(err);
						uni.$u.toast(err.message)
						uni.stopPullDownRefresh();
					}
				});
			},

			// 刷新消息列表，获取最新消息
			refreshMessages() {
				console.log('刷新消息列表，获取最新消息');
				// 清空nextMessageId，以便获取最新消息
				this.nextMessageId = '';
				// 清空现有消息列表
				this.messageList = [];
				// 重新获取历史消息
				YeIMUniSDK.getInstance().getHistoryMessageList({
					conversationId: this.conversation.conversationId,
					success: (res) => {
						console.log('获取到最新消息:', res);
						if (res.data && res.data.list) {
							this.messageList = res.data.list;
							// 滚动到底部
							this.pageScrollToBottom(0);

							// 检查是否有语音消息和AI回复
							if (this.messageList.length >= 2) {
								console.log('消息列表长度:', this.messageList.length);
								console.log('最新消息:', this.messageList[this.messageList.length - 1]);
								console.log('倒数第二条消息:', this.messageList[this.messageList.length - 2]);
							}
						}
					},
					fail: (err) => {
						console.error('获取最新消息失败:', err);
					}
				});
			},
			// 处理语音按钮点击
			handleVoiceClick() {
				console.log("handleVoiceClick 被点击，当前状态:", this.isVoiceInput, "isAiReplying:", this.isAiReplying);

				if (this.isAiReplying) {
					console.log("AI正在回复中，忽略语音按钮点击");
					uni.showToast({
						title: 'AI正在回复中，请稍候',
						icon: 'none'
					});
					return;
				}

				this.switchVoiceInput();
			},

			// 处理表情按钮点击
			handleFaceClick() {
				// 简单的状态检查
				if (this.isAiReplying) {
					uni.showToast({
						title: 'AI正在回复中，请稍候',
						icon: 'none'
					});
					return;
				}

				if (this.recording) {
					return;
				}

				// 直接调用face方法
				this.face();
			},

			//切换语音、文字输入方式
			switchVoiceInput() {
				console.log("switchVoiceInput 被调用，当前状态:", this.isVoiceInput, "isAiReplying:", this.isAiReplying);

				this.isVoiceInput = !this.isVoiceInput;
				if (this.isVoiceInput) {
					this.voiceIcon = 'https://mobile-**********.cos.ap-guangzhou.myqcloud.com/yeim/ic_footer_keyboard.png';
					console.log("切换到语音输入模式");
				} else {
					this.voiceIcon = 'https://mobile-**********.cos.ap-guangzhou.myqcloud.com/yeim/ic_footer_voice.png';
					console.log("切换到文字输入模式");
				}
			},
			// 处理语音开始事件
			handleVoiceBegin(e) {
				console.log("handleVoiceBegin 被触发", e);
				console.log("当前状态 - isAiReplying:", this.isAiReplying, "isVoiceInput:", this.isVoiceInput);

				if (this.isAiReplying) {
					console.log("AI正在回复中，忽略录音操作");
					uni.showToast({
						title: 'AI正在回复中，请稍候',
						icon: 'none'
					});
					return;
				}

				this.voiceBegin(e);
			},

			// 处理语音移动事件
			handleVoiceIng(e) {
				if (this.isAiReplying) return;
				this.voiceIng(e);
			},

			// 处理语音结束事件
			handleVoiceEnd(e) {
				if (this.isAiReplying) return;
				this.voiceEnd(e);
			},

			// 处理语音取消事件
			handleVoiceCancel(e) {
				if (this.isAiReplying) return;
				this.voiceCancel(e);
			},

			voiceBegin: function(e) {
				console.log("voiceBegin 被触发", e);
				console.log("当前状态 - isAiReplying:", this.isAiReplying, "isVoiceInput:", this.isVoiceInput);

				if (e.touches.length > 1) {
					console.log("多点触控，取消录音");
					return;
				}
				this.initPoint.Y = e.touches[0].pageY;
				this.initPoint.identifier = e.touches[0].identifier;

				// 检查录音权限
				this.checkRecordPermission().then(() => {
					console.log("录音权限检查通过，开始启动录音管理器");
					try {
						recorderManager.start({
							// format: "aac"
							format: 'wav',
							duration: 60000, // 最大时长（单位毫秒）
							sampleRate: 44100, // 采样率（可选）
							numberOfChannels: 1, // 声道数（可选）
							encodeBitRate: 96000, // 码率（可选）
						});
						console.log("录音管理器启动命令已发送");
					} catch (error) {
						console.error("启动录音失败:", error);
						uni.showToast({
							title: '录音启动失败',
							icon: 'none'
						});
					}
				}).catch((error) => {
					console.error("录音权限检查失败:", error);
					uni.showToast({
						title: '需要录音权限才能发送语音',
						icon: 'none'
					});
				});
			},
			//录音开始UI效果
			recordBegin: function(e) {
				this.recording = true;
				this.voiceTis = '松开 结束';
				this.recordLength = 0;
				this.recordTimer = setInterval(() => {
					this.recordLength++;
				}, 1000)
			},
			// 录音被打断
			voiceCancel: function() {
				uni.hideLoading();
				this.recording = false;
				this.voiceTis = '按住 说话';
				this.recordTis = '手指上滑 取消发送'
				this.recordWillStop = true; //不发送录音
				recorderManager.stop(); //录音结束
			},
			// 录音中(判断是否触发上滑取消发送)
			voiceIng: function(e) {
				if (!this.recording) {
					return;
				}
				let touche = e.touches[0];
				if (this.initPoint.Y - touche.pageY >= uni.upx2px(100)) {
					this.recordWillStop = true;
					this.recordTis = '松开手指 取消发送'
					console.log("取消录音")
				} else {
					this.recordWillStop = false;
					this.recordTis = '手指上滑 取消发送'
				}
			},
			// 结束录音
			voiceEnd: function(e) {
				if (!this.recording) {
					return;
				}
				uni.hideLoading();
				this.recording = false;
				this.voiceTis = '按住 说话';
				this.recordTis = '手指上滑 取消发送'
				recorderManager.stop(); //录音结束
			},
			//录音结束(回调文件)
			recordEnd: function(e) {
				console.log("录音结束(回调文件)", e)
				clearInterval(this.recordTimer);
				if (!this.recordWillStop) {
					uni.getFileInfo({
						filePath: e.tempFilePath,
						success: (file) => {
							let msg = {
								length: "0",
								duration: this.recordLength * 1000,
								tempFilePath: e.tempFilePath,
								fileSize: file.size
							}
							let min = parseInt(this.recordLength / 60);
							let sec = this.recordLength % 60;
							min = min < 10 ? '0' + min : min;
							sec = sec < 10 ? '0' + sec : sec;
							this.sendAudioMessage(msg);
						}
					})
				} else {
					console.log('取消发送录音', e);
				}
				this.recordWillStop = false;
			},
			//发送语音消息
			async sendAudioMessage(e) {
				console.log("sendAudioMessage", e)
				const file = {
					path: e.tempFilePath,
					size: e.fileSize,
					fileName: `voice_${Date.now()}.m4a`
				}
				let url = await uploadForm(file)
				console.log("sendAudioMessage", url)

				//创建语音消息
				// let message = YeIMUniSDK.getInstance().createAudioMessage({
				// 	toId: this.conversation.conversationId, //接收者用户ID字符串
				// 	conversationType: this.conversation.type, //消息类型：私聊
				// 	body: {
				// 		file: {
				// 			tempFilePath: e.tempFilePath, //本地录音临时文件
				// 			duration: parseInt(e.duration / 1000), // 录音时长，单位：秒
				// 		}
				// 	},
				// 	onProgress: (progress) => {
				// 		console.log('上传进度' + progress.progress);
				// 		console.log('已经上传的数据长度' + progress.totalBytesSent);
				// 		console.log('预期需要上传的数据总长度' + progress.totalBytesExpectedToSend);
				// 	}
				// });

				//创建语音直发消息
				let message = YeIMUniSDK.getInstance().createAudioMessageFromUrl({
					toId: this.conversation.conversationId, //接收者用户ID字符串
					conversationType: this.conversation.type, //会话类型：私聊
					body: {
						audioUrl: url, //音频网络Url
						duration: parseInt(e.duration / 1000), //音频时长，单位：秒
					},
					extra: "这是拓展的自定义的内容"
				});

				console.log("发送的语音消息：", message)

				//发送消息
				YeIMUniSDK.getInstance().sendMessage({
					message: message,
					success: (res) => {
						this.insertMessage(res.data);
						this.pageScrollToBottom();
					},
					fail: (err) => {
						console.log(err)
					}
				});
			},
			//发送文字消息
			sendTextMessage(e) {
				this.hideOtherPanel()
				// let text = e.detail.value;
				let text = this.inputText
				if (text != "") {
					let message = YeIMUniSDK.getInstance().createTextMessage({
						toId: this.conversation.conversationId,
						conversationType: this.conversation.type,
						body: {
							text: text
						},
						extra: "这是自定义的内容啊嗷嗷！！"
					});
					YeIMUniSDK.getInstance().sendMessage({
						message: message,
						success: (res) => {
							this.insertMessage(res.data);
							this.pageScrollToBottom();
						},
						fail: (err) => {
							console.log(err)
							// 发送失败 
						}
					});
				}
				this.inputText = "";
			},

			// 发送语音转文字消息
			sendVoiceTextMessage(text) {
				if (text && text.trim() !== "") {
					// 尝试解码URL编码的文本
					try {
						// 检查文本是否是URL编码的
						if (text.indexOf('%') !== -1) {
							text = decodeURIComponent(text);
							console.log('解码后的文本:', text);
						}
					} catch (e) {
						console.error('解码URL编码的文本失败:', e);
						// 解码失败时保持原文本
					}

					console.log('发送语音转文字消息:', text);

					// 显示提示
					uni.showToast({
						title: '语音已转为文字',
						icon: 'none',
						duration: 1500
					});

					// 创建文本消息
					let message = YeIMUniSDK.getInstance().createTextMessage({
						toId: this.conversation.conversationId,
						conversationType: this.conversation.type,
						body: {
							text: text
						},
						extra: "语音转文字消息"
					});

					// 发送消息
					YeIMUniSDK.getInstance().sendMessage({
						message: message,
						success: (res) => {
							console.log('语音转文字消息发送成功:', res);
							this.insertMessage(res.data);
							this.pageScrollToBottom();

							// 清除本地存储中的语音消息
							try {
								uni.removeStorageSync('last_voice_message');
							} catch (e) {
								console.error('清除本地语音消息失败:', e);
							}
						},
						fail: (err) => {
							console.error('语音转文字消息发送失败:', err);
							uni.showToast({
								title: '消息发送失败',
								icon: 'none'
							});
						}
					});
				} else {
					console.error('语音转文字消息为空，无法发送');
				}
			},
			hideOtherPanel() {
				this.isMoreViewShow = false;
				this.isFaceViewShow = false;
				// 面板关闭后稍微调整滚动位置
				setTimeout(() => {
					this.scrollToBottom();
				}, 350);
			},
			face() {
				this.isFaceViewShow = true;  // 直接打开表情面板
				this.isMoreViewShow = false; // 关闭更多面板
				// 延迟滚动到底部，确保面板动画完成后再滚动
				setTimeout(() => {
					this.scrollToBottom();
				}, 350);
			},
			more() {
				this.isMoreViewShow = true;  // 直接打开更多面板
				this.isFaceViewShow = false; // 关闭表情面板
				// 延迟滚动到底部，确保面板动画完成后再滚动
				setTimeout(() => {
					this.scrollToBottom();
				}, 350);
			},

			// 强制重置状态的方法
			forceResetState() {
				this.isAiReplying = false;
				this.recording = false;
				this.isFaceViewShow = false;
				this.isMoreViewShow = false;
				this.voiceTis = '按住 说话';
				this.recordTis = '手指上滑 取消发送';
				this.lastFaceClickTime = 0;
				uni.hideLoading();
			},

			// 检查录音权限
			checkRecordPermission() {
				return new Promise((resolve, reject) => {
					// #ifdef MP-WEIXIN
					uni.getSetting({
						success: (res) => {
							console.log("当前权限设置:", res.authSetting);
							if (res.authSetting['scope.record'] === false) {
								// 用户之前拒绝了录音权限，需要引导用户去设置页面开启
								uni.showModal({
									title: '需要录音权限',
									content: '请在设置中开启录音权限，以便发送语音消息',
									confirmText: '去设置',
									success: (modalRes) => {
										if (modalRes.confirm) {
											uni.openSetting({
												success: (settingRes) => {
													if (settingRes.authSetting['scope.record']) {
														resolve();
													} else {
														reject(new Error('用户未开启录音权限'));
													}
												},
												fail: () => {
													reject(new Error('打开设置页面失败'));
												}
											});
										} else {
											reject(new Error('用户取消开启录音权限'));
										}
									}
								});
							} else if (res.authSetting['scope.record'] === undefined) {
								// 用户还没有授权过，直接请求授权
								uni.authorize({
									scope: 'scope.record',
									success: () => {
										console.log("录音权限授权成功");
										resolve();
									},
									fail: () => {
										console.log("录音权限授权失败");
										reject(new Error('录音权限授权失败'));
									}
								});
							} else {
								// 用户已经授权了录音权限
								console.log("录音权限已授权");
								resolve();
							}
						},
						fail: (err) => {
							console.error("获取权限设置失败:", err);
							reject(err);
						}
					});
					// #endif

					// #ifndef MP-WEIXIN
					// 非微信小程序环境，直接通过
					resolve();
					// #endif
				});
			},

			// 测试录音功能
			testRecord() {
				console.log("=== 测试录音功能 ===");
				console.log("录音管理器:", recorderManager);

				this.checkRecordPermission().then(() => {
					console.log("权限检查通过，开始测试录音");
					uni.showToast({
						title: '开始测试录音',
						icon: 'none'
					});

					try {
						recorderManager.start({
							format: 'wav',
							duration: 5000, // 5秒测试
							sampleRate: 44100,
							numberOfChannels: 1,
							encodeBitRate: 96000,
						});

						// 5秒后自动停止
						setTimeout(() => {
							recorderManager.stop();
						}, 5000);

					} catch (error) {
						console.error("测试录音失败:", error);
						uni.showToast({
							title: '测试录音失败: ' + error.message,
							icon: 'none'
						});
					}
				}).catch((error) => {
					console.error("录音权限检查失败:", error);
					uni.showToast({
						title: '录音权限检查失败',
						icon: 'none'
					});
				});
			},
			chooseImage() {
				this.isMoreViewShow = false;
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album'], //从相册选择
					success: async (res) => {
						let imageInfo = await uni.getImageInfo({
							src: res.tempFilePaths[0]
						})
						let message = YeIMUniSDK.getInstance().createImageMessage({
							toId: this.conversation.conversationId,
							conversationType: this.conversation.type,
							body: {
								file: {
									tempFilePath: res.tempFilePaths[0],
									width: imageInfo[1].width,
									height: imageInfo[1].height
								}
							},
							onProgress: (progress) => {
								console.log('上传进度' + progress.progress);
								console.log('已经上传的数据长度' + progress.totalBytesSent);
								console.log('预期需要上传的数据总长度' + progress.totalBytesExpectedToSend);
							}
						});
						YeIMUniSDK.getInstance().sendMessage({
							message: message,
							success: (res) => {
								console.log(res);
								this.insertMessage(res.data);
								this.pageScrollToBottom();
							},
							fail: (err) => {
								console.log(err)
							}
						});
					}
				});
			},
			capImage() {
				this.isMoreViewShow = false;
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ['camera'], //从相册选择
					success: async (res) => {
						let imageInfo = await uni.getImageInfo({
							src: res.tempFilePaths[0]
						})
						let message = YeIMUniSDK.getInstance().createImageMessage({
							toId: this.conversation.conversationId,
							conversationType: this.conversation.type,
							body: {
								file: {
									tempFilePath: res.tempFilePaths[0],
									width: imageInfo[1].width,
									height: imageInfo[1].height
								}
							},
							onProgress: (progress) => {
								console.log('上传进度' + progress.progress);
								console.log('已经上传的数据长度' + progress.totalBytesSent);
								console.log('预期需要上传的数据总长度' + progress.totalBytesExpectedToSend);
							}
						});
						YeIMUniSDK.getInstance().sendMessage({
							message: message,
							success: (res) => {
								console.log(res);
								this.insertMessage(res.data);
								this.pageScrollToBottom();
							},
							fail: (err) => {
								console.log(err)
							}
						});
					}
				});
			},
			chooseVideo() {
				this.isMoreViewShow = false;
				uni.chooseVideo({
					count: 1, //默认9 
					sourceType: ['album'], //从相册选择
					success: async (res) => {
						console.log(res)
						let message = YeIMUniSDK.getInstance().createVideoMessage({
							toId: this.conversation.conversationId,
							conversationType: this.conversation.type,
							body: {
								file: {
									tempFilePath: res.tempFilePath,
									width: res.width,
									height: res.height,
									duration: parseInt(res.duration)
								}
							},
							onProgress: (progress) => {
								console.log(progress)
								console.log('上传进度' + progress.progress);
								console.log('已经上传的数据长度' + progress.totalBytesSent);
								console.log('预期需要上传的数据总长度' + progress.totalBytesExpectedToSend);
							}
						});
						YeIMUniSDK.getInstance().sendMessage({
							message: message,
							success: (res) => {
								this.insertMessage(res.data);
								this.pageScrollToBottom();
							},
							fail: (err) => {
								console.log(err)
							}
						});
						console.log(message)
					}
				});
			},
			chooseLocation() {
				this.isMoreViewShow = false;
				uni.chooseLocation({
					success: (location) => {
						let message = YeIMUniSDK.getInstance().createLocationMessage({
							toId: this.conversation.conversationId, //接收者用户ID字符串
							conversationType: this.conversation.type, //消息类型：私聊
							body: {
								address: location.name, //地址名称
								description: location.address, //地址详细描述
								longitude: location.longitude, //经度
								latitude: location.latitude //纬度
							}
						});

						//发送消息
						YeIMUniSDK.getInstance().sendMessage({
							message: message,
							success: (res) => {
								console.log(res);
								this.insertMessage(res.data);
								this.pageScrollToBottom();
							},
							fail: (err) => {
								console.log(err)
							}
						});

					}
				});
			},
			parseText(text) {
				if(text){
					var regex = /\[(.+?)\]/g;
					let emojis_char = text.match(regex);
					if (emojis_char) {
						for (let i = 0; i < emojis_char.length; i++) {
							let char = emojis_char[i];
							char = char.replace("[", "");
							char = char.replace("]", "");
							let index = char.split("_")[1];
							let image =
								"<img style='width:20px;height:20px' src='	https://mobile-**********.cos.ap-guangzhou.myqcloud.com/yeim/face/Expression_" +
								index +
								"@2x.png' />";
							text = text.replace(emojis_char[i], image);
						}
					}
				}
				return text;
			},
			/**
			 * 播放或停止语音消息
			 * @param {Object} e 消息对象
			 */
			playAudio(e) {
				console.log(e)
				// 如果没有实例，先创建
				if (!this.audioContext) {
					this.audioContext = uni.createInnerAudioContext();
					this.audioContext.onPlay(() => {
						this.isAudioPlaying = true;
					});
					this.audioContext.onStop(() => {
						this.isAudioPlaying = false;
						this.playingAudioId = null;
					});
					this.audioContext.onEnded(() => {
						this.isAudioPlaying = false;
						this.playingAudioId = null;
					});
					this.audioContext.onError((res) => {
						this.isAudioPlaying = false;
						this.playingAudioId = null;
						console.log(res);
					});
				}

				// 如果正在播放且是同一条消息，则停止
				if (this.isAudioPlaying && this.playingAudioId === e.messageId) {
					this.audioContext.stop();
					return;
				}

				// 否则切换到新音频并播放
				this.audioContext.src = e.body.audioUrl;
				console.log("audio-src", e.body.audioUrl)
				this.playingAudioId = e.messageId;
				this.audioContext.play();
			},
			playAudio_bak(e) {
				const innerAudioContext = uni.createInnerAudioContext();
				innerAudioContext.autoplay = true;
				innerAudioContext.src = e.body.audioUrl;
				innerAudioContext.onPlay(() => {
					console.log('开始播放');
				});
				innerAudioContext.onError((res) => {
					console.log(res);
				});
			},
			emojiClick(i) {
				let char = '[表情_' + i + ']';
				this.inputText = this.inputText + char;
			},
			// 预览图片
			previewImage(item) {
				const imageUrl = item.body.imageUrl || item.body.thumbnailUrl;
				if (imageUrl) {
					uni.previewImage({
						urls: [imageUrl],
						current: imageUrl,
						fail: (err) => {
							uni.showToast({
								title: '图片预览失败',
								icon: 'none'
							});
						}
					});
				} else {
					uni.showToast({
						title: '图片地址无效',
						icon: 'none'
					});
				}
			},
			// 图片加载成功
			onImageLoad(e) {
				// 静默处理
			},
			// 图片加载失败
			onImageError(e) {
				uni.showToast({
					title: '图片加载失败',
					icon: 'none',
					duration: 1500
				});
			},
			// 获取系统信息
			getSystemInfo() {
				try {
					const systemInfo = uni.getSystemInfoSync();
					this.systemInfo = systemInfo;

					// 获取安全区域信息
					if (systemInfo.safeAreaInsets) {
						this.safeAreaInsets = systemInfo.safeAreaInsets;
					}
				} catch (e) {
					// 静默处理错误
				}
			},
			// 滚动到聊天底部
			scrollToBottom() {
				this.$nextTick(() => {
					uni.pageScrollTo({
						scrollTop: 999999,
						duration: 300
					});
				});
			},

			pageScrollToBottom(duration = 100) {
				this.$nextTick(() => {
					setTimeout(() => {
						uni.pageScrollTo({
							scrollTop: 9999999,
							duration: duration
						});
					}, 100)
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		background-color: #f7f7f7;

		.record-item {
			display: flex;
			// align-items: start;
			width: 100%;

			.avatar {
				width: 90rpx;
				height: 90rpx;
				border-radius: 90rpx;
			}

			.right {
				display: flex;
				flex-direction: column;
				justify-content: center;

				.nickname {
					margin: 0 20rpx 10rpx 20rpx;

					text {
						font-size: 26rpx;
					}
				}

				.message-box {
					margin: 0 20rpx;

					.text-box {
						display: flex;
						align-items: center;
						max-width: 60vw;
						min-height: 50rpx;
						background-color: #ffffff;
						border-radius: 20rpx;
						padding: 20rpx;
					}

					.text-bg-right {
						background-color: #6e64c5;
						color: black;
					}

					.image-box {
						cursor: pointer;
						transition: transform 0.2s ease;

						&:active {
							transform: scale(0.95);
						}

						image {
							width: 300rpx;
							height: 300rpx;
							border-radius: 10rpx;
							display: block;
						}
					}

					.audio-box {
						width: 300rpx;
						display: flex;
						align-items: center;
						min-height: 50rpx;
						background-color: #ffffff;
						border-radius: 20rpx;
						padding: 20rpx;

						image {
							width: 50rpx;
							height: 50rpx;
						}

						text {
							font-size: 28rpx;
							padding: 0 20rpx;
						}
					}

					.audio-bg-right {
						background-color: #b7bbfa;
						flex-direction: row-reverse;
						-webkit-flex-direction: row-reverse;

						text {
							color: black;
						}
					}

					.video-box {
						video {
							width: 220rpx;
							height: 380rpx;
							border-radius: 10rpx;
						}
					}

					.location-box {
						width: 450rpx;
						display: flex;
						flex-direction: column;
						justify-content: center;
						background-color: #ffffff;
						border-top-left-radius: 10rpx;
						border-top-right-radius: 10rpx;

						.location-header {
							display: flex;
							flex-direction: column;
							padding: 20rpx;

							text:first-child {
								font-size: 32rpx;
								overflow: hidden;
								-webkit-line-clamp: 1;
								text-overflow: ellipsis;
								display: -webkit-box;
								-webkit-box-orient: vertical;
							}

							text:last-child {
								font-size: 24rpx;
								color: #999;
								overflow: hidden;
								-webkit-line-clamp: 2;
								text-overflow: ellipsis;
								display: -webkit-box;
								-webkit-box-orient: vertical;
							}
						}

						.location-map {
							width: 450rpx;
							height: 200rpx;

							image {
								width: 450rpx;
								height: 200rpx;
							}
						}
					}
				}
			}


		}

		.record-sys-tips-item {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			margin: 0rpx 0;

			text {
				font-size: 24rpx;
				color: #7d7d7d;
			}
		}

		.row-right {
			flex-direction: row-reverse;
			-webkit-flex-direction: row-reverse;

		}

		.footer {
			position: fixed;
			bottom: 0;
			z-index: 8888;
			display: flex;
			align-items: center;
			background-color: #ffffff;
			min-height: 100rpx;
			padding: 0 20rpx;
			width: -webkit-fill-available;

			/* 安全区域适配 */
			&.footer-safe-area {
				padding-bottom: env(safe-area-inset-bottom);
				padding-bottom: constant(safe-area-inset-bottom); /* iOS < 11.2 */
			}

			.voice {
				height: auto;
				line-height: 0;

				image {
					width: 60rpx;
					height: 60rpx;
				}
			}

			.input {
				background-color: #f7f7f7;
				border-radius: 50rpx;
				margin: 0 20rpx 0rpx 20rpx;
				flex: 1;

				.uni-input {
					padding: 14rpx 22rpx;
				}
			}

			.voice-input {
				background-color: #f7f7f7;
				border-radius: 50rpx;
				margin: 0 20rpx 0rpx 20rpx;
				flex: 1;
				padding: 14rpx 22rpx;
				display: flex;
				justify-content: center;
				align-items: center;

				text {
					font-size: 32rpx;
					color: #282828;
				}
			}

			.voice-input:active {
				background-color: #f1f1f1;
			}

			.voice-input.disabled {
				background-color: #e0e0e0;
				color: #999;
				pointer-events: none;
			}

			.input input:disabled {
				background-color: #f0f0f0;
				color: #999;
			}

			.voice.disabled,
			.face.disabled,
			.more.disabled {
				opacity: 0.5;
				pointer-events: none;
			}

			.face {
				height: auto;
				line-height: 0;
				cursor: pointer;
				padding: 10rpx;
				margin-right: 20rpx;

				&:not(.disabled) {
					pointer-events: auto;
				}

				image {
					width: 60rpx;
					height: 60rpx;
					pointer-events: none;
				}
			}

			.more {
				height: auto;
				line-height: 0;
				cursor: pointer;
				padding: 10rpx;

				&:not(.disabled) {
					pointer-events: auto;
				}

				image {
					width: 60rpx;
					height: 60rpx;
					pointer-events: none;
				}
			}
		}
	}

	.record {
		position: fixed;
		bottom: 150px;
		width: 350rpx;
		left: 200rpx;
		height: 350rpx;
		background-color: #4c4c4c;
		border-radius: 20rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		.gif {
			top: 50rpx;
			position: absolute;
			text-align: center;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
		}

		.recording {
			width: 150rpx;
			height: 150rpx;
		}

		.recordTis {
			bottom: 50rpx;
			position: absolute;
			display: flex;
			flex-direction: column;
			text-align: center;
			justify-content: center;
		}

		.recordTis-text {
			color: white;
			font-size: 30rpx;
		}
	}

	.emoji-view {
		width: 100%;
		height: 600rpx;
		background-color: #ffffff;
		position: fixed;
		z-index: 9999;
		bottom: -600rpx;
		transition: bottom 0.3s ease;

		/* 显示状态 */
		&.emoji-show {
			bottom: 0px;
		}

		/* 安全区域适配 */
		&.emoji-safe-area {
			padding-bottom: env(safe-area-inset-bottom);
			padding-bottom: constant(safe-area-inset-bottom); /* iOS < 11.2 */
		}
	}

	.more-view {
		width: 100%;
		height: 600rpx;
		background-color: #ffffff;
		position: fixed;
		z-index: 9999;
		bottom: -600rpx;
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		transition: bottom 0.3s ease;

		/* 显示状态 */
		&.more-show {
			bottom: 0px;
		}

		/* 安全区域适配 */
		&.more-safe-area {
			padding-bottom: env(safe-area-inset-bottom);
			padding-bottom: constant(safe-area-inset-bottom); /* iOS < 11.2 */
		}

		.more-tool {
			width: 24.5%;
			height: 49%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			.more-icon {
				background-color: #f7f7f7;
				width: 130rpx;
				height: 130rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 20rpx;

				image {
					width: 70rpx;
					height: 70rpx;
				}
			}

			.more-text {
				padding-top: 10rpx;
				font-size: 30rpx;
				color: #575757;
			}
		}
	}

	/deep/.uni-list {
		padding-bottom: calc(120rpx + env(safe-area-inset-bottom)); /* 动态计算底部间距 */
		padding-bottom: calc(120rpx + constant(safe-area-inset-bottom)); /* iOS < 11.2 兼容 */
		transition: padding-bottom 0.3s ease; /* 平滑过渡动画 */

		/* 当面板打开时增加更多底部间距 */
		&.list-panel-open {
			padding-bottom: calc(720rpx + env(safe-area-inset-bottom)); /* 面板高度600rpx + 操作栏120rpx + 安全区域 */
			padding-bottom: calc(720rpx + constant(safe-area-inset-bottom)); /* iOS < 11.2 兼容 */
		}
	}

	.panel-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.3);
		z-index: 8000;
	}

	/deep/.uni-list,
	.uni-list-item,
	.uni-list-item__container {
		background-color: unset !important;
	}

	/deep/.uni-list-item__container {
		background-color: #f7f7f7 !important;
	}

	/deep/.uni-input-input,
	.uni-input-placeholder {
		font-size: 28rpx !important;
		;
	}

	.float-btn {
		position: fixed;
		right: 30rpx;
		bottom: 200rpx;
		width: 100rpx;
		height: 100rpx;
		z-index: 999;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
		}
	}

	.float-button-inner {
		width: 85rpx;
		height: 85rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 60rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.4);
		animation: float 3s ease-in-out infinite;
	}
</style>
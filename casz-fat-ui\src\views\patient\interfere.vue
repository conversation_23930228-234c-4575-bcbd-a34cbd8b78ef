<template>
  <div class="intervention-container">
    <!-- 顶部标题 -->
    <el-card class="header-card">
      <div class="header-content">
        <div class="header-left">
          <i class="el-icon-user-solid header-icon"></i>
          <span class="patient-id">患者姓名：{{ patientName }}</span>
        </div>
        <el-button type="primary" size="mini" @click="openTemplateDialog" class="header-button">
          选择干预计划
        </el-button>
      </div>
    </el-card>

    <el-dialog title="选择干预计划" :visible.sync="templateDialogVisible" width="80%" append-to-body>
      <indexSel :patient-id="patientId" v-if="templateDialogVisible" @refresh-parent="fetchInterventionList"
        @close-dialog="handleCloseDialog" />
    </el-dialog>


    <!-- 时间轴展示 -->
    <el-card class="timeline-card">
      <el-timeline>
        <el-timeline-item v-for="(item, index) in interventionList" :key="index"
          :timestamp="formatTimelineTimestamp(item)" placement="top">
          <el-card class="timeline-card-item">
            <div class="card-header">
              <span class="operation-type">{{ item.operationType }}</span>
              <div class="status-and-button">
                <el-tag :type="item.status === '已处理' ? 'success' : 'warning'">
                  {{ item.status }}
                </el-tag>
                <el-button style="margin-left: 10px" v-if="item.status === '待处理' || item.status === '处理中'" type="danger"
                  size="mini" @click="cancelTask(item.id)">
                  取消
                </el-button>
              </div>
            </div>

            <!-- AI随访特殊布局 -->
            <div class="card-content" v-if="item.operationType === 'AI随访'">
              <div class="ai-follow-item">
                <!-- 左侧内容 -->
                <div class="ai-follow-content">
                  <div class="ai-follow-type">
                    随访类型：{{ formatFollowType(item.followType) }}
                  </div>
                  <div class="ai-follow-result">
                    <i class="el-icon-s-opportunity result-icon"></i>
                    <span class="result-text">{{ item.result }}</span>
                  </div>
                </div>
                <!-- 右侧按钮 -->
                <div class="ai-follow-button"
                  v-if="[1, 2, 3].includes(Number(item.followType)) && item.status === '已处理'">
                  <el-button type="primary" size="mini" @click="viewDetail(item)">查看详情</el-button>
                </div>
              </div>
            </div>

            <!-- 普通内容 -->
            <div class="card-content" v-else-if="item.operationType !== ''">
              <i class="el-icon-s-opportunity result-icon"></i>
              <span class="result-text">{{ item.result }}</span>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-card>
    <!-- 详情对话框 -->
    <el-dialog :title="infoTitle" :visible.sync="infOpen" width="900px" @close="infOpen = false" append-to-body>
      <dynamicFormEdit v-if="form.formText" :buildData="form.formText" :value="form.formValue" :disabled="true" />
    </el-dialog>
  </div>
</template>

<script>
import { getListByPatientId, cancelInterventionTask } from "@/api/follow/task";
import { getExamResult } from "@/api/exam/examResult";
import indexSel from "@/views/follow/temp/indexSel.vue";
import dynamicFormEdit from "@/views/common/businessForm/dynamicFormEdit";
export default {
  name: "InterventionPlan",
  components: { dynamicFormEdit, indexSel },
  props: {
    patientId: {
      type: String,
      required: true
    },
    typeName: {
      type: String,
      default: "未知病种"
    }
  },
  data() {
    return {
      interventionList: [],
      patientName: '',
      loading: false,
      infOpen: false,
      templateDialogVisible: false,
      infoTitle: "",
      form: {
        formText: "",
        formValue: "",
        followType: ""
      }
    };
  },
  methods: {
    handleCloseDialog() {
      this.templateDialogVisible = false; // 关闭弹窗
    },
    openTemplateDialog() {
      this.templateDialogVisible = true;
    },
    formatTimelineTimestamp(item) {
      if (item.dayType === 2) {
        return '每天';
      } else if ([3, 4, 5].includes(Number(item.dayType))) {
        // 假设下次触发时间为当前时间 + distanceDays 天
        if (item.time && item.distanceDays) {
          const triggerDate = item.time;
          // const nextTriggerDate = new Date(triggerDate);
          //     nextTriggerDate.setDate(nextTriggerDate.getDate() + item.distanceDays);
          return `触发时间：${triggerDate}`;
        } else {
          return '触发时间：未知';
        }
      } else {
        return item.time;
      }
    },
    formatDate(date) {
      if (!date) return "未知时间";
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0'); // 月份从0开始，+1后补零
      const day = String(d.getDate()).padStart(2, '0');
      const hours = String(d.getHours()).padStart(2, '0');
      const minutes = String(d.getMinutes()).padStart(2, '0');

      return `${year}年${month}月${day}日 ${hours}:${minutes}`;
    },
    formatFollowType(followType) {
      switch (Number(followType)) {
        case 1:
          return '体征';
        case 2:
          return '问卷';
        case 3:
          return '诊后';
        case 4:
          return '闲时';
        default:
          return '未知类型';
      }
    },
    async fetchInterventionList() {
      this.loading = true;
      try {
        const response = await getListByPatientId({
          patientId: this.patientId,
          typeName: this.typeName
        });

        // 将接口返回的 CaszManagerTask[] 转换为 interventionList 格式
        this.interventionList = response.data.map(item => ({
          id: item.id,
          time: this.formatDate(item.triggerTime),
          operationType: item.managerKey,
          result: item.managerValue,
          examId: item.examId,
          status: this.formatStatus(item.status),
          dayType: item.dayType,
          followId: item.followId,
          followType: item.followType,
          patientName: item.patientName,
          distanceDays: item.distanceDays
        }));

        this.patientName = response.data[0]?.patientName || '';
      } catch (error) {
        console.error("获取干预记录失败:", error);
        // this.$message.error("获取干预记录失败");
      } finally {
        this.loading = false;
      }
    },
    async cancelTask(id) {
      try {
        await this.$confirm('确定要取消此任务吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        // 调用 API 取消任务
        await cancelInterventionTask(id);
        this.$message.success('任务已取消');
        // 刷新列表
        await this.fetchInterventionList();
      } catch (error) {
        console.error("取消任务失败:", error);
        // this.$message.error("取消任务失败");
      }
    },
    async viewDetail(item) {
      try {
        // 1. 设置标题
        this.infoTitle = "问卷详情";

        // 2. 调用接口获取详情数据（假设接口返回 formText 和 formValue）
        // const response = await getExamResult(item.id); // 替换为实际接口
        // const response = await getExamResult('1873626170768019458'); // 替换为实际接口
        const response = await getExamResult(item.examId);
        this.form = {
          formText: response.data.formText,  // 表单配置
          formValue: response.data.formValue, // 表单值
          followType: item.followType        // 可选：用于区分类型
        };

        // 3. 打开弹窗
        this.infOpen = true;
      } catch (error) {
        console.error("获取详情失败:", error);
        this.$message.error("获取详情失败");
      }
    },
    formatStatus(status) {
      switch (status) {
        case 0:
          return "待处理";
        case 1:
          return "处理中";
        case 2:
          return "已处理";
        case 3:
          return "已取消";
        default:
          return "未知状态";
      }
    }
  },

  async created() {

    // if (this.patientId && this.templateId) {
    this.loading = true;
    try {
      this.patientId = this.$route.params.id;
      const response = await getListByPatientId({
        patientId: this.patientId,
        typeName: this.typeName
      });
      // 将接口返回的 CaszManagerTask[] 转换为 interventionList 格式
      this.interventionList = response.data.map(item => ({
        id: item.id,
        time: this.formatDate(item.triggerTime),        // 对应 triggerTime
        operationType: item.managerKey,                // 对应 managerKey
        result: item.managerValue,                    // 对应 managerValue
        examId: item.examId,
        dayType: item.dayType,
        status: this.formatStatus(item.status),        // 根据 status 转换
        followId: item.followId,                     // 可选字段
        followType: item.followType,
        patientName: item.patientName,
        distanceDays: item.distanceDays              // 可选字段
      }));
      this.patientName = response.data[0].patientName
    } catch (error) {
      console.error("获取干预记录失败:", error);
      // this.$message.error("获取干预记录失败");
    } finally {
      this.loading = false;
    }
    // }
  }
};
</script>


<style scoped>
.intervention-container {
  padding: 20px;
}

.header-card {
  margin-bottom: 20px;
  border-left: 4px solid #409EFF;
}

.header-content {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #333;
}

.header-icon {
  font-size: 20px;
  margin-right: 10px;
  color: #409EFF;
}

.patient-id {
  font-weight: bold;
}

.timeline-card {
  padding: 20px;
  background-color: #f9f9f9;
}

.timeline-card-item {
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-and-button {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  /* 子元素右对齐 */
  margin-left: auto;
  /* 容器整体右对齐 */
}


.operation-type {
  font-size: 15px;
  font-weight: bold;
  color: #1a1a1a;
}

.status-tag {
  font-size: 12px;
  padding: 4px 8px;
}

.card-content {
  padding: 12px 16px;
  background-color: #fff;
  border: 1px dashed #eee;
}

.result-icon {
  color: #409EFF;
  margin-right: 8px;
  font-size: 16px;
}

.result-text {
  font-size: 14px;
  color: #555;
}

.ai-follow-item {
  display: flex;
  justify-content: space-between;
  /* 左右对齐 */
  align-items: center;
  /* 垂直居中 */
  padding: 12px 16px;
  background-color: #fff;
  border: 1px dashed #eee;
}

.ai-follow-type {
  font-size: 14px;
  color: #666;
}

.ai-follow-result {
  font-size: 14px;
  color: #555;
}

.ai-follow-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.ai-follow-button {
  align-self: flex-end;
  /* 按钮靠右对齐 */
  margin-left: auto;
  /* 强制右侧对齐 */
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  /* 左对齐 */
}

.header-content .el-button {
  margin-left: auto;
  /* 右对齐 */
}

.header-left {
  display: flex;
  align-items: center;
}

.header-button {
  margin-left: auto;
  /* 确保按钮靠右 */
}
</style>

package com.ruoyi.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.web.domain.CaszPatient;
import com.ruoyi.web.domain.CaszPatientDiease;
import com.ruoyi.web.domain.vo.CaszPatientDieaseVo;
import com.ruoyi.web.mapper.CaszPatientDieaseMapper;
import com.ruoyi.web.mapper.CaszPatientMapper;
import com.ruoyi.web.mapper.CaszScreenFormMapper;
import com.ruoyi.web.service.ICaszPatientDieaseService;
import com.ruoyi.web.service.ICaszWarnRecordService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 患者疾病Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-15
 */
@Service
public class CaszPatientDieaseServiceImpl extends ServiceImpl<CaszPatientDieaseMapper,CaszPatientDiease> implements ICaszPatientDieaseService
{
    @Resource
    private CaszPatientDieaseMapper caszPatientDieaseMapper;
    @Resource
    private CaszPatientMapper caszPatientMapper;
//    @Resource
//    protected Validator validator;
    @Resource
    private CaszScreenFormMapper screenFormMapper;
    @Autowired
    private ICaszWarnRecordService warnRecordService;

    /**
     * 查询患者疾病
     *
     * @param id 患者疾病主键
     * @return 患者疾病
     */
    @Override
    public CaszPatientDiease selectCaszPatientDieaseById(String id)
    {
        return caszPatientDieaseMapper.selectCaszPatientDieaseById(id);
    }

    /**
     * 查询患者疾病列表
     *
     * @param caszPatientDiease 患者疾病
     * @return 患者疾病
     */
    @Override
    public List<CaszPatientDiease> selectCaszPatientDieaseList(CaszPatientDiease caszPatientDiease)
    {
        return caszPatientDieaseMapper.selectCaszPatientDieaseList(caszPatientDiease);
    }

    /**
     * 新增患者疾病
     *
     * @param caszPatientDiease 患者疾病
     * @return 结果
     */
    @Override
    public int insertCaszPatientDiease(CaszPatientDiease caszPatientDiease)
    {
        caszPatientDiease.setCreateTime(DateUtils.getNowDate());
        return caszPatientDieaseMapper.insertCaszPatientDiease(caszPatientDiease);
    }

    /**
     * 修改患者疾病
     *
     * @param caszPatientDiease 患者疾病
     * @return 结果
     */
    @Override
    public int updateCaszPatientDiease(CaszPatientDiease caszPatientDiease)
    {
        caszPatientDiease.setUpdateTime(DateUtils.getNowDate());
        return caszPatientDieaseMapper.updateCaszPatientDiease(caszPatientDiease);
    }

    /**
     * 批量删除患者疾病
     *
     * @param ids 需要删除的患者疾病主键
     * @return 结果
     */
    @Override
    public int deleteCaszPatientDieaseByIds(String[] ids)
    {
        return caszPatientDieaseMapper.deleteCaszPatientDieaseByIds(ids);
    }

    /**
     * 删除患者疾病信息
     *
     * @param id 患者疾病主键
     * @return 结果
     */
    @Override
    public int deleteCaszPatientDieaseById(String id)
    {
        return caszPatientDieaseMapper.deleteCaszPatientDieaseById(id);
    }

    @Override
    public String importPatientDiease(List<CaszPatientDiease> dieases, boolean updateSupport, String operName) {
        return "";
        //        if (StringUtils.isNull(dieases) || dieases.size() == 0) {
//            throw new ServiceException("导入糖尿病情况数据不能为空！");
//        }
//        int successNum = 0;
//        int failureNum = 0;
//        StringBuilder successMsg = new StringBuilder();
//        StringBuilder failureMsg = new StringBuilder();
//        for (CaszPatientDiease diease : dieases) {
//            try {
//                // 验证是否存在这个随访记录
//
//                String rfId = diease.getRfId();
//                String string = EncryptDecryptUtils.encryptString(rfId);
//
//                LambdaQueryWrapper<CaszPatient> patientLambdaQueryWrapper = new LambdaQueryWrapper<>();
//                patientLambdaQueryWrapper.eq(CaszPatient::getRfId,string);
//                CaszPatient patient = caszPatientMapper.selectOne(patientLambdaQueryWrapper);
//                if(patient!=null){
//                    String idNo = patient.getIdNo();
//                    String string1 = EncryptDecryptUtils.encryptString(idNo);
//                    LambdaQueryWrapper<CaszPatientDiease> examWrapper = new LambdaQueryWrapper<>();
//                    examWrapper.eq(CaszPatientDiease::getIdNo,string1);
//                    examWrapper.eq(CaszPatientDiease::getUnitId,patient.getAssignHos());
//                    examWrapper.eq(CaszPatientDiease::getDieaseType,0);
////                    examWrapper.eq(CaszPatientDiease::getIdNo,query.getIdNo());
//
//                    CaszPatientDiease dieaseOne = caszPatientDieaseMapper.selectOne(examWrapper);
//                    if (StringUtils.isNull(dieaseOne))
//                    {
//                        BeanValidators.validateWithException(validator, diease);
//                        //创建体检记录
//                        diease.setCreateBy(operName);
//                        diease.setCreateTime(new Date());
//                        diease.setPatientId(patient.getId());
//                        diease.setPatientId(patient.getId());
//                        diease.setLastBloodSugar(diease.getLastBloodSugar()!=null?diease.getLastBloodSugar().replace("，","."):null);
//                        diease.setUnitId(patient.getAssignHos());
//                        diease.setName(patient.getName());
//                        diease.setIdNo(patient.getIdNo());
//                        caszPatientDieaseMapper.insert(diease);
//
//                        //            随机血糖正常值为3.9～11.1mmol/L。
//                        String lastBloodSugar = diease.getLastBloodSugar();
//                        if(StringUtils.isNotEmpty(lastBloodSugar)){
//                            lastBloodSugar = lastBloodSugar.replace("，",".");
//                            Double aDouble = Double.valueOf(lastBloodSugar);
//                            CaszWarnRecord warnRecord = new CaszWarnRecord();
//                            warnRecord.setUnitId(patient.getAssignHos());
//                            warnRecord.setCreateTime(new Date());
//                            warnRecord.setCgmRecordId(diease.getId());
//                            warnRecord.setTenantId(lastBloodSugar);
//                            warnRecord.setPatientId(patient.getId());
//                            warnRecord.setPatientName(patient.getName());
//                            warnRecord.setCreateBy("system");
//                            if(aDouble<3.9){
//                                warnRecord.setWarnType("0");
//                                warnRecord.setContent("您的末次血糖糖值为"+lastBloodSugar+"mmol/L，低于3.9mmol/L，存在低血糖风险，请留意！");
//                                warnRecordService.save(warnRecord);
//                            }else if(aDouble>11.1){
//                                warnRecord.setWarnType("1");
//                                warnRecord.setContent("您的末次血糖糖值为"+lastBloodSugar+"mmol/L，高于11.1mmol/L，存在高血糖风险，请留意！");
//                                warnRecordService.save(warnRecord);
//                            }
//                        }
//
//                        successNum++;
//                        successMsg.append("<br/>" + successNum + "、档案号 " + diease.getRfId()+":"+diease.getDieaseType() + " 记录导入成功");
//                    }
//                    else if (updateSupport)
//                    {
//                        BeanValidators.validateWithException(validator, diease);
//                        dieaseOne.setUpdateBy(operName);
//                        dieaseOne.setUpdateTime(new Date());
//                        dieaseOne.setDieaseLevel(diease.getDieaseLevel());
//                        dieaseOne.setNowStatus(diease.getNowStatus());
//                        dieaseOne.setCommUsePill(diease.getCommUsePill());
//                        dieaseOne.setRelateDiease(diease.getRelateDiease());
//                        dieaseOne.setComplication(diease.getComplication());
//                        dieaseOne.setStartTime(diease.getStartTime());
//                        dieaseOne.setAddr(diease.getAddr());
//                        dieaseOne.setStandManager(diease.getStandManager());
//                        dieaseOne.setLastBloodSugar(diease.getLastBloodSugar()!=null?diease.getLastBloodSugar().replace("，","."):null);
//                        dieaseOne.setExamNum(diease.getExamNum());
//                        dieaseOne.setFollowNum(diease.getFollowNum());
//                        dieaseOne.setStartTime(diease.getStartTime());
//                        dieaseOne.setDieaseCourse(diease.getDieaseCourse());
//                        caszPatientDieaseMapper.updateById(dieaseOne);
//
//                        String lastBloodSugar = diease.getLastBloodSugar();
//                        if(StringUtils.isNotEmpty(lastBloodSugar)){
//                            LambdaQueryWrapper<CaszWarnRecord> warnRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
//                            warnRecordLambdaQueryWrapper.eq(CaszWarnRecord::getCgmRecordId,dieaseOne.getId());
//                            warnRecordLambdaQueryWrapper.eq(CaszWarnRecord::getPatientId,patient.getId());
//                            List<CaszWarnRecord> list = warnRecordService.list(warnRecordLambdaQueryWrapper);
//                            if(list.size()<=0){
//                                lastBloodSugar = lastBloodSugar.replace("，",".");
//                                Double aDouble = Double.valueOf(lastBloodSugar);
//                                CaszWarnRecord warnRecord = new CaszWarnRecord();
//                                warnRecord.setUnitId(patient.getAssignHos());
//                                warnRecord.setCreateTime(new Date());
//                                warnRecord.setCgmRecordId(diease.getId());
//                                warnRecord.setPatientId(patient.getId());
//                                warnRecord.setPatientName(patient.getName());
//                                warnRecord.setCreateBy("system");
//                                warnRecord.setTenantId(lastBloodSugar);
//                                if(aDouble<3.9 && aDouble>0.1){
//                                    warnRecord.setWarnType("0");
//                                    warnRecord.setContent("您的末次血糖糖值为"+lastBloodSugar+"mmol/L，低于3.9mmol/L，存在低血糖风险，请留意！");
//                                    warnRecordService.save(warnRecord);
//                                }else if(aDouble>11.1){
//                                    warnRecord.setWarnType("1");
//                                    warnRecord.setContent("您的末次血糖糖值为"+lastBloodSugar+"mmol/L，高于11.1mmol/L，存在高血糖风险，请留意！");
//                                    warnRecordService.save(warnRecord);
//                                }
//                            }
//                        }
//                        successNum++;
//                        successMsg.append("<br/>" + successNum + "、档案号 " + diease.getRfId()+":"+diease.getDieaseType() + " 记录更新成功");
//                    }
//                    else
//                    {
////                            failureNum++;
//                        failureMsg.append("<br/>" + failureNum + "、档案号 " + diease.getRfId()+":"+diease.getDieaseType()+ " 已存在");
//                    }
//
//                }else{
//                    failureNum++;
//                    failureMsg.append("<br/>" + failureNum + "、档案号 " + diease.getRfId()+" 的患者不存在");
//                    System.err.println("<br/>" + failureNum + "、档案号 " + diease.getRfId()+" 的患者不存在");
//                }
//
//            }
//            catch (Exception e)
//            {
//                failureNum++;
//                String msg = "<br/>" + failureNum + "、患者 " + "、档案号 " + diease.getRfId()+":"+diease.getDieaseType() +" 导入失败：";
//                failureMsg.append(msg + e.getMessage());
//                log.error(msg, e);
//            }
//        }
//        if (failureNum > 0)
//        {
//            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
//            throw new ServiceException(failureMsg.toString());
//        }
//        else
//        {
//            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
//        }
//        return successMsg.toString();
    }

    @Override
    public List<CaszPatientDieaseVo> selectPatientsByDiease(CaszPatientDiease caszPatientDiease) {
        return caszPatientDieaseMapper.selectPatientsByDiease(caszPatientDiease);
    }

    @Override
    public List<CaszPatient> selectRecDieasePatients(CaszPatientDiease caszPatientDiease) {
        return caszPatientDieaseMapper.selectRecDieasePatients(caszPatientDiease);
    }
}

<template>
	<view class="index-content">
		<Navbar ref="navbar" :hideBtn="true"
			titleIcon="https://fat-**********.cos.ap-nanjing.myqcloud.com/weixin/down.png" :title="unit.unitName"
			@titleClick="titleClick" bgColor="#b7bbfa" :h5Show="false" :fixed="true" :titleStyle="{color:'#FFFFFF'}">
		</Navbar>
		<view class="index-serach" :style="{top:statusBarHeight}" v-if="false">
			<u-search placeholder="请输入搜索内容" v-model="keyword" :showAction="false" color="#FFFFFF" :inputStyle="{'background-color': 'transparent',
					'color': '#FFFFFF'}" bgColor="transparent" :clearabled="false" searchIconColor="#FFF" :searchIconSize="24">
			</u-search>
		</view>
		<!-- 新的趋势轮播图组件 -->
		<TrendSwiper ref="trendSwiper"></TrendSwiper>
		<class-room :courses="list"></class-room>
		<!-- AI智能分析报告 -->
		<view class="ai-analysis-module" @click="navigateToAnalysis()" v-if="$hasAnyRoles('patient')">
			<view class="ai-content">
				<view class="ai-title">AI智能分析报告</view>
			</view>
			<view class="ai-icon">
				<u-icon size="30" color="#fff"
					name="https://fat-**********.cos.ap-nanjing.myqcloud.com/weixin/aifk.png"></u-icon>
			</view>
		</view>
		<!-- 新横向滑动tabs -->
		<view class="tabs-sticky-container" :style="{ top: tabsTop + 'px' }">
			<scroll-view class="custom-tabs-scroll" scroll-x ref="tabsScrollView" :scroll-left="scrollLeft">
				<view class="custom-tabs" ref="tabsContainer">
					<view v-for="(tab, idx) in tabsList" :key="tab.id"
						:class="['custom-tab-item', { 'custom-tab-active': tabsIndex === idx }]"
						@click="changeTabs(tab.id, idx)" :ref="`tab-${idx}`">
						{{ tab.name }}
					</view>
					<!-- 透明容器确保右侧边距 -->
					<view class="custom-tabs-spacer"></view>
				</view>
			</scroll-view>
		</view>
		<article-list :articles="articles" @toDetail="toDetail" @loadmore="loadmoreArticle"></article-list>
		<u-loadmore
			v-if="articles.length > 0"
			:status="status"
			:loading-text="loadingText"
			:loadmore-text="loadmoreText"
			:nomore-text="nomoreText"
		/>
		<u-back-top :scroll-top="scrollTop"></u-back-top>
		<u-popup :show="xyShow" :round="10" mode="center" :customStyle="{width:'85%'}">
			<view style="margin:0 29px">
				<text
					style="display: block;font-weight: bold;font-size: 20px;margin-top: 15px;text-align: center;">使用提醒</text>
				<text style="display: block;margin-top: 15px;font-size: 14px;line-height: 25px;text-align: justify;">
					系统检测到您还未授权绑定微信小程序，需要授权绑定后才能接收微信小程序消息提醒！
				</text>
				<view style="display: flex;flex-direction: row;margin:20px 0;">
					<button style="width: 40%;" type="primary" size="mini" @click="xyShow = false">下次</button>
					<button type="default" size="mini" style="width: 40%;background-color: #fff;color:#0090d0"
						@click="wxLogin">授权</button>
				</view>
			</view>
		</u-popup>
		<u-picker confirmColor="#4f41ac" :show="unitShow" title="选择切换医疗机构" :defaultIndex="unitDefaultIndex"
			:columns="units" @confirm="unitConfirm" @close="unitShow=false" @cancel="unitShow=false"
			keyName="unitName"></u-picker>
		<!-- AI食物识别悬浮按钮 -->
		<view class="ai-float-button" @click="openFoodRecognition" v-if="show">
			<view class="float-button-inner">
				<u-icon name="camera-fill" color="#FFFFFF" size="28"></u-icon>
				<text class="float-button-text">AI算热量</text>
			</view>
		</view>
		<!-- AI食物识别组件 -->
		<FoodRecognition :show="showFoodRecognition" @close="closeFoodRecognition" @save="onSaveFoodRecord">
		</FoodRecognition>
		<tabBar v-if="show" :isDoctor="$hasAnyRoles('doctor,hos')" :pyqTitle="pyqTitle"></tabBar>
	</view>
</template>
<script>
	import Navbar from '@/components/navbar/Navbar'
	import articleList from "@/components/article-list/article-list.vue"
	import classRoom from "@/components/class-room/class-room.vue";
	import TrendSwiper from "@/components/trend-swiper/trend-swiper.vue";
	import FoodRecognition from "@/components/food-recognition/index.vue";
	import config from '@/common/config'
	import {
		share
	} from "@/utils/common.js"
	import {
		businessLogin
	} from "@/api/login";
	import md5 from "md5";
	import * as WxApi from "@/api/wxApi"
	import IMSDK from "openim-uniapp-polyfill";
	import * as IndexApi from "@/api/index"
	import appConfig from "@/common/config";
	import * as PatientApi from "@/api/patient.js"
	import {
		checkLoginError
	} from "@/utils/common";
	import * as UserApi from "@/api/user.js"
	let _now = new Date();
	let now_time = {};
	now_time.year = _now.getFullYear()
	now_time.month = _now.getMonth() + 1
	now_time.day = _now.getDay()
	export default {
		components: {
			Navbar,
			articleList,
			classRoom,
			TrendSwiper,
			FoodRecognition
		},
		data() {
			return {
				tabShow: false,
				unitDefaultIndex: [0],
				unitShow: false,
				units: [],
				unitId: '',
				id: 0,
				patientInfo: {},
				hosName: '数智助手',
				show: false,
				isAuth: false,
				xyShow: false,
				user: {},
				queryParams: {
					pageSize: 20,
					pageNum: 1,
					status: 1,
					mainType: 1,
					unitId: '',
				},
				total: 0,
				status: 'nomore',
				loadingText: '努力加载中',
				loadmoreText: '',
				nomoreText: '没有更多了',
				articles: [],
				list: [{
					name: '糖尿病概况',
					thumb: 'http://************/dev-api/profile/upload/2024/05/13/casz_logo_20240513165503A003.png'
				}],
				lbt: [],
				scrollTop: 0,
				loginInfo: {
					phoneNumber: "18084787925",
					password: "666666",
					areaCode: 86
				},
				keyword: '',
				statusBarHeight: '',
				tabsList: [],
				tabsIndex: 0,
				scrollLeft: 0,
				title: '数智助手',
				unit: {
					unitName: '数智助手'
				},
				// AI食物识别相关
				showFoodRecognition: false,
				pyqTitle:'胖友圈',
				// 滚动位置保存相关
				savedScrollTop: 0,
				isReturningFromDetail: false
			}
		},
		created() {
			this.isAuth = false;
			share()			
		},
		onLoad(option) {
			if (option && option.scene) {
				uni.setStorageSync("scanUnitId", option.scene)
			}
		},
		onHide() {
			this.show = false;
		},
		onUnload() {
			this.show = false;
		},
		onShow() {
			// 检查是否从文章详情页返回
			if (this.isReturningFromDetail) {
				this.isReturningFromDetail = false;
				// 恢复滚动位置
				this.$nextTick(() => {
					if (this.savedScrollTop > 0) {
						uni.pageScrollTo({
							scrollTop: this.savedScrollTop,
							duration: 0
						});
					}
				});
				return;
			}

			this.queryParams.pageNum = 1
			this.articles = []
			this.status = 'loadmore'
			this.unitId = this.$store.state.unit.id;
			this.$store.commit('SET_TAB_NAME', '知识科普')
			this.show = true;
			uni.hideTabBar({
				animation: false
			})
			this.pyqTitle = this.$store.state.pyqTabName
			// 检查权限
			this.checkPermissions();
			let token = uni.getStorageSync("AccessToken")
			if (token) {
				this.getPatientInfo();
				this.getInfo();
				this.getRecognitionCountInfo(); // 获取AI识别次数
			} else {
				this.getTabList()
				this.getIndexCourse()
				this.hosName = '数智助手'
				this.title = '数智助手'
				this.unit = {
					unitName: '数智助手'
				}
			}
			// 刷新趋势轮播图数据
			this.$nextTick(() => {
				if (this.$refs.trendSwiper) {
					this.$refs.trendSwiper.refreshData();
				}
			});
		},
		onReady() {
			this.getIndexLbt();
		},
		mounted() {
			this.statusBarHeight = this.$refs.navbar.statusBarHeight
		},
		// 监听页面滚动
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		computed: {
			tabsTop() {
				// 使用 uni.$u.sys() 获取状态栏高度，更可靠
				const statusBarHeight = uni.$u.sys().statusBarHeight || 0;
				const navbarContentHeight = 44; // Navbar 内容高度
				return statusBarHeight + navbarContentHeight;
			}
		},
		methods: {
			// 重置滚动位置保存状态
			resetScrollState() {
				this.isReturningFromDetail = false;
				this.savedScrollTop = 0;
			},

			// 检查权限
			async checkPermissions() {
				try {
					// 检查位置权限
					await this.checkLocationPermission();
					// 检查录音权限
					await this.checkRecordPermission();
					console.log('所有权限检查完成');
				} catch (error) {
					console.log('权限检查过程中出现问题:', error);
				}
			},
			// 检查位置权限
			checkLocationPermission() {
				return new Promise((resolve, reject) => {
					uni.getSetting({
						success: (res) => {
							console.log('位置权限设置:', res.authSetting);

							if (res.authSetting['scope.userLocation'] === true) {
								// 已经授权位置权限
								console.log('位置权限已授权');
								resolve();
							} else if (res.authSetting['scope.userLocation'] === false) {
								// 用户之前拒绝过位置权限，引导用户到设置页面
								console.log('位置权限被拒绝，需要手动开启');
								this.showLocationPermissionDialog();
								reject(new Error('LOCATION_PERMISSION_DENIED'));
							} else {
								// 未授权过，尝试请求授权
								console.log('首次请求位置权限');
								uni.authorize({
									scope: 'scope.userLocation',
									success: () => {
										console.log('位置权限授权成功');
										resolve();
									},
									fail: () => {
										console.log('位置权限授权失败');
										this.showLocationPermissionDialog();
										reject(new Error('LOCATION_PERMISSION_DENIED'));
									}
								});
							}
						},
						fail: (err) => {
							console.error('获取位置权限设置失败:', err);
							reject(new Error('GET_LOCATION_SETTING_FAILED'));
						}
					});
				});
			},
			// 检查录音权限
			checkRecordPermission() {
				return new Promise((resolve, reject) => {
					uni.getSetting({
						success: (res) => {
							console.log('录音权限设置:', res.authSetting);

							if (res.authSetting['scope.record'] === true) {
								// 已经授权录音权限
								console.log('录音权限已授权');
								resolve();
							} else if (res.authSetting['scope.record'] === false) {
								// 用户之前拒绝过录音权限，引导用户到设置页面
								console.log('录音权限被拒绝，需要手动开启');
								this.showRecordPermissionDialog();
								reject(new Error('RECORD_PERMISSION_DENIED'));
							} else {
								// 未授权过，尝试请求授权
								console.log('首次请求录音权限');
								uni.authorize({
									scope: 'scope.record',
									success: () => {
										console.log('录音权限授权成功');
										resolve();
									},
									fail: () => {
										console.log('录音权限授权失败');
										this.showRecordPermissionDialog();
										reject(new Error('RECORD_PERMISSION_DENIED'));
									}
								});
							}
						},
						fail: (err) => {
							console.error('获取录音权限设置失败:', err);
							reject(new Error('GET_RECORD_SETTING_FAILED'));
						}
					});
				});
			},

			// 显示位置权限提示对话框
			showLocationPermissionDialog() {
				uni.showModal({
					title: '需要位置权限',
					content: '为了提供更好的服务，需要获取您的位置信息。请在设置中开启位置权限。',
					confirmText: '去设置',
					cancelText: '暂不开启',
					success: (res) => {
						if (res.confirm) {
							// 用户点击去设置，打开设置页面
							uni.openSetting({
								success: (settingRes) => {
									console.log('位置权限设置页面返回:', settingRes.authSetting);
									if (settingRes.authSetting['scope.userLocation']) {
										uni.showToast({
											title: '位置权限已开启',
											icon: 'success'
										});
									}
								},
								fail: (err) => {
									console.error('打开设置页面失败:', err);
								}
							});
						}
					}
				});
			},
			// 显示录音权限提示对话框
			showRecordPermissionDialog() {
				uni.showModal({
					title: '需要录音权限',
					content: '语音助手功能需要录音权限才能使用。请在设置中开启录音权限。',
					confirmText: '去设置',
					cancelText: '暂不开启',
					success: (res) => {
						if (res.confirm) {
							// 用户点击去设置，打开设置页面
							uni.openSetting({
								success: (settingRes) => {
									console.log('录音权限设置页面返回:', settingRes.authSetting);
									if (settingRes.authSetting['scope.record']) {
										uni.showToast({
											title: '录音权限已开启',
											icon: 'success'
										});
									}
								},
								fail: (err) => {
									console.error('打开设置页面失败:', err);
								}
							});
						}
					}
				});
			},
			unitConfirm(pickerValue) {
				// 切换医疗机构时重置滚动位置保存状态
				this.resetScrollState();

				this.tabShow = false;
				this.articles = []
				this.status = 'loadmore'
				this.tabsIndex = 0;
				this.id = 0;
				this.unitShow = false;
				this.$store.commit('SET_UNIT', pickerValue.value[0])
				this.unit = pickerValue.value[0]
				let patient = {
					assignHos: this.unit.id,
					id: this.unit.unionId,
				}
				UserApi.updatePatientUnit(patient).then(res => {
					if (res.code == 200) {
						this.getTabList()
						this.getIndexLbt();
					}
				})
				uni.$u.toast("切换成功")
				this.updateDefaultUnit();
			},
			updateDefaultUnit() {
				PatientApi.updateDefaultUnit(this.unit.id).then(res => {
					if (res.code == 200) {}
				})
			},
			titleClick() {
				if (this.units[0].length > 1) {
					this.units = []
					UserApi.patientUnits().then(res => {
						if (res.code == 200) {
							this.units.push(res.data)
							this.unitShow = true;
						}
					})
				} else {
					uni.$u.toast("没有可切换机构")
				}
			},
			getUnitInfo() {
				this.units = []
				UserApi.patientUnits().then(res => {
					if (res.code == 200) {
						this.units.push(res.data)
						this.unit = this.units[0][0]
						this.unitId = this.unit.id
						this.updateDefaultUnit()
						this.$store.commit('SET_UNIT', this.unit)
						this.getTabList()
						this.getIndexCourse()
					}
				})
			},
			tabClick(e) {},
			getPatientInfo() {
				UserApi.getPatientInfo().then(res => {
					if (res.code == 200) {
						this.patientInfo = res.data;
						this.getUnitInfo()
						if (this.patientInfo && this.patientInfo.status == 1) {
							this.isAuth = true;
						}
					}
				})
			},
			share() {
				// #ifdef MP-WEIXIN
				wx.showShareMenu({
					withShareTicket: true,
					menus: ['shareAppMessage', 'shareTimeline']
				})
				// #endif
			},
			onShareAppMessage(res) {
				return {
					title: '数智助手',
				}
			},
			onShareTimeline(res) {
				return {
					title: '数智助手',
				}
			},
			getTabList() {
				let TagList = {
					status: 1,
					unitId: this.$store.state.unit.id
				}
				IndexApi.wxIndexTypeList(TagList).then(res => {
					let defaultIndex = 0;
					this.tabsList = [{
						index: defaultIndex,
						name: '全部',
						id: 0
					}]

					this.getWxIndexInfoList()
					if (res.code == 200) {
						let tabs = res.rows;
						let _this = this;
						tabs.forEach(item => {
							defaultIndex++;
							_this.tabsList.push({
								index: defaultIndex,
								name: item.catalogName,
								id: item.id
							})
						})
						_this.tabShow = true;
					}
				}).catch(err => {})
			},
			getIndexLbt() {
				let query = {
					pageSize: 100,
					pageNum: 1,
					status: 1,
					mainType: 0,
					unitId: this.$store.state.unit.id,
				}
				IndexApi.lbtList(query).then(res => {
					if (res.code == 401) {
						const app = this
						this.$store.dispatch('Logout').then(res => {
							uni.reLaunch({
								url: '/pages/login/index'
							})
						})
						return;
					}
					if (res.code == 200) {
						let data = res.rows;
						this.lbt = []
						let _this = this;
						data.forEach(item => {
							let images = item.images;
							_this.lbt.push(images)
							if (images) {
								let arr = images.split(",")
								item.images = arr.map(item => {
									if (!item.startsWith("http")) {
										return appConfig.getPicUrl() +
											item;
									} else {
										return item;
									}
								})
								item.image = item.images[0]
								item.title = item.name;
							}
						})
					}
				})
			},
			loadmoreArticle() {
				if (this.articles.length < this.total) {
					this.queryParams.pageNum++
					this.status = 'loading'
					this.getWxIndexInfoList()
				} else {
					this.status = 'nomore'
				}
			},

			getWxIndexInfoList() {
				this.queryParams.catalogId = this.id
				this.queryParams.unitId = this.unit.id
				this.queryParams.searchValue = this.unit.unionId
				IndexApi.wxIndexList(this.queryParams).then(res => {
					if (res.code == 200) {
						this.total = res.total;
						let data = res.rows;
						data.forEach(item => {
							let images = item.images;
							if (images) {
								let arr = images.split(",")
								if (arr.length <= 2) {
									item.mainType = 'img1'
								} else {
									item.mainType = 'img3'
								}
								item.images = arr.map(item => {
									if (item.indexOf("https") === -1) {
										return appConfig.getPicUrl() + item
									} else {
										return item;
									}
								})
							} else {
								item.mainType = 'nopic'
							}
						})
						this.articles = [...this.articles, ...data];
						if (this.queryParams.pageNum * this.queryParams.pageSize < this
							.total) {
							this.status = "loadmore"
						} else {
							this.status = "nomore"
						}
					}
				})
			},
			scrollBottom() {
				if (this.queryParams.pageNum < this.total) {
					this.queryParams.pageNum++;
					this.status = 'loading '
					this.getWxIndexInfoList()
				} else {
					this.status = 'nomore'
				}
			},
			getIndexCourse() {
				let query = {
					patientId: this.$store.state.unit.unionId
				}
				console.log('this.$store.state.unit.unionId2:', this.$store.state.unit.unionId);
				IndexApi.getIndexCourse(query).then(res => {
					if (res.code == 200) {
						this.list = res.data
						this.list.forEach(item => {
							if (item.images) {
								item.thumb = appConfig.getPicUrl() + item.images.split(",")[0]
							}
						})
					}
				})
			},
			toDetail(item) {
				// 保存当前滚动位置
				this.savedScrollTop = this.scrollTop;
				this.isReturningFromDetail = true;

				uni.navigateTo({
					url: '/pageIndex/articleInfo?id=' + item.id + "_info"
				})
				let wenzhang = {
					id: item.id,
					readNum: item.readNum += 1
				}
				UserApi.updateReadNum(wenzhang).then(res => {})
			},
			left() {},
			right() {},
			getInfo() {
				const app = this
				app.$store.dispatch('Info').then(res => {
					app.user = res.user
					if(app.user.userId==135){
						this.$store.commit('SET_PYQ_NAME', '更多功能')
						this.pyqTitle="更多功能"
					}else{
						this.$store.commit('SET_PYQ_NAME', '胖友圈')
						this.pyqTitle="胖友圈"
					}
					if (this.$hasAnyRoles("doctor,hos")) {
						uni.switchTab({
							url: '/pages/concat/concat'
						})
					} else {
						
						this.loginInfo.phoneNumber = app.user.phonenumber
						// this.imLogin()
					}
				})
			},
			saveLoginInfo() {
				uni.setStorage({
					key: "lastPhoneNumber",
					data: this.loginInfo.phoneNumber,
				});
				uni.setStorage({
					key: "lastAreaCode",
					data: this.loginInfo.areaCode,
				});
			},
			saveLoginProfile(data) {
				const {
					imToken,
					chatToken,
					userID
				} = data;
				uni.setStorage({
					key: "IMUserID",
					data: userID,
				});
				uni.setStorage({
					key: "IMToken",
					data: imToken,
				});
				uni.setStorage({
					key: "BusinessToken",
					data: chatToken,
				});
			},
			swiperChange() {},
			swiperClick(item) {},
			async imLogin() {
				this.saveLoginInfo();
				let data = {};
				let platform;
				try {
					// #ifdef H5
					platform = 5
					// #endif
					// #ifdef MP-WEIXIN
					platform = 6
					// #endif
					// #ifdef APP-PLUS
					platform = uni.$u.os() === "ios" ? 1 : 2
					// #endif
					data = await businessLogin({
						phoneNumber: this.loginInfo.phoneNumber,
						areaCode: `+${this.loginInfo.areaCode}`,
						password: md5(this.loginInfo.phoneNumber + '@!'),
						platform,
						verifyCode: this.loginInfo.verificationCode,
					});
					const {
						imToken,
						userID
					} = data.data;
					this.checkOpenId(userID)

					// #ifdef H5 || MP-WEIXIN
					await IMSDK.asyncApi(IMSDK.IMMethods.Login, IMSDK.uuid(), {
						userID,
						token: imToken,
						platformID: platform,
						wsAddr: config.getWsUrl(),
						apiAddr: config.getApiUrl(),
					});
					// #endif
					// #ifdef APP-PLUS
					await IMSDK.asyncApi(IMSDK.IMMethods.Login, IMSDK.uuid(), {
						userID,
						token: imToken,
					});
					// #endif
					this.saveLoginProfile(data.data);
					this.$store.commit("SET_AUTH_DATA", data.data);
					this.$store.dispatch("getSelfInfo");
					this.$store.dispatch("getConversationList");
					this.$store.dispatch("getUnReadCount");
					this.$store.dispatch("getFriendList");
					this.$store.dispatch("getGrouplist");
					this.$store.dispatch("getBlacklist");
					this.$store.dispatch("getRecvFriendApplications");
					this.$store.dispatch("getSentFriendApplications");
					this.$store.dispatch("getRecvGroupApplications");
					this.$store.dispatch("getSentGroupApplications");
					this.loginInfo.password = "";
				} catch (err) {
					uni.$u.toast(checkLoginError(err));
				}
			},
			changeTabs(id, index) {
				// 切换标签页时重置滚动位置保存状态
				this.resetScrollState();

				this.queryParams.pageNum = 1
				this.articles = []
				this.status = 'loadmore'
				this.id = id;
				this.tabsIndex = index
				this.getWxIndexInfoList()

				// 自动滚动到选中的tab居中
				this.$nextTick(() => {
					this.scrollToCenter(index)
				})
			},

			// 滚动到指定tab居中
			scrollToCenter(index) {
				if (this.tabsList.length <= 1) return

				setTimeout(() => {
					const query = uni.createSelectorQuery().in(this)

					// 获取scroll-view容器宽度
					query.select('.custom-tabs-scroll').boundingClientRect()
					// 获取所有tab元素的位置信息
					query.selectAll('.custom-tab-item').boundingClientRect()

					query.exec((res) => {
						console.log('查询结果:', res)

						if (!res || !res[0] || !res[1] || !res[1][index]) {
							console.log('查询失败，使用估算方式')
							this.scrollToCenterFallback(index)
							return
						}

						const scrollViewRect = res[0]
						const tabRects = res[1]
						const currentTab = tabRects[index]

						// 计算当前tab相对于容器左边的位置
						let tabLeft = 0
						for (let i = 0; i < index; i++) {
							if (tabRects[i]) {
								tabLeft += tabRects[i].width
								// 加上margin-right，在小程序中大约是7.5px
								tabLeft += 7.5
							}
						}

						// 计算目标滚动位置，让当前tab居中
						const scrollViewWidth = scrollViewRect.width
						const tabWidth = currentTab.width
						const targetScrollLeft = tabLeft + tabWidth / 2 - scrollViewWidth / 2

						// 边界处理
						const totalTabsWidth = tabRects.reduce((total, rect) => total + rect.width + 7.5,
							0)
						const maxScrollLeft = Math.max(0, totalTabsWidth - scrollViewWidth)
						const finalScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft))

						console.log('滚动计算:', {
							index,
							tabLeft,
							tabWidth,
							scrollViewWidth,
							targetScrollLeft,
							finalScrollLeft
						})

						this.scrollLeft = finalScrollLeft
					})
				}, 100) // 延迟100ms确保DOM更新完成
			},

			// 备用滚动方法（估算）
			scrollToCenterFallback(index) {
				const systemInfo = uni.getSystemInfoSync()
				const screenWidth = systemInfo.screenWidth

				// 估算每个tab的宽度
				let totalWidth = 0
				for (let i = 0; i <= index; i++) {
					const tab = this.tabsList[i]
					if (tab) {
						// 根据文字长度估算宽度：字体26rpx + padding 48rpx + margin 15rpx
						const textLength = tab.name.length
						const estimatedWidth = (textLength * 13 + 24 + 7.5) * (screenWidth / 375) // 375是iPhone6的屏幕宽度
						totalWidth += estimatedWidth
					}
				}

				// 计算目标滚动位置
				const targetScrollLeft = totalWidth - screenWidth / 2
				const finalScrollLeft = Math.max(0, targetScrollLeft)

				console.log('备用滚动:', {
					index,
					screenWidth,
					totalWidth,
					finalScrollLeft
				})

				this.scrollLeft = finalScrollLeft
			},
			checkOpenId(userId) {
				UserApi.getImUserByUserID(userId).then(res => {
					if (res.code == 200 && res.data && !res.data.openId) {
						this.xyShow = true;
					}
				})
			},
			getAuthorizeInfo() {
				return new Promise((resolve, reject) => {
					uni.getSetting({
						success: res => {
							if (res.authSetting['scope.userInfo']) {
								uni.getUserInfo({
									provider: 'weixin',
									success: ({
										userInfo
									}) => {
										resolve(userInfo)
									},
									fail(e) {
										reject(e)
									}
								})
							} else {
								uni.authorize({
									scope: 'scope.userInfo',
									success: e => {},
									fail: e => {
										uni.openSetting({
											success(res) {

											}
										})
									}
								})
							}
						},
						fail(e) {
							reject(e)
						}
					})
				})
			},
			wxLogin(e) {
				this.getAuthorizeInfo().then(res => {})
				uni.login({
					provider: 'weixin', //使用微信登录
					success: loginRes => {
						const code = loginRes.code
						WxApi.login({
							code,
							unitId: this.unit.id
						}).then((res) => {
							uni.setStorageSync('openid', res.data.openid)
							uni.showToast({
								title: "已完成帐号与微信绑定"
							})
							this.xyShow = false;
						})
					}
				})
			},

			// AI食物识别相关方法
			openFoodRecognition() {
				this.showFoodRecognition = true
			},

			closeFoodRecognition() {
				this.showFoodRecognition = false
			},

			onSaveFoodRecord(result) {
				console.log('食物记录保存结果:', result)
				if (result.success) {
					console.log('保存成功，后端返回数据:', result.data)
					// 可以在这里添加保存成功后的处理逻辑
					// 比如刷新饮食记录列表、显示成功提示等
				} else {
					console.error('保存失败:', result)
				}
			},

			// AI智能化分析导航
			navigateToAnalysis() {
				uni.navigateTo({
					url: '/pageWork/statics/cgm'
				})
			}
		}
	}
</script>
<style lang="scss">
	.scroll-list {
		@include flex(column);

		&__goods-item {
			margin-right: 20px;

			&__image {
				width: 60px;
				height: 60px;
				border-radius: 4px;
			}

			&__text {
				color: #306A98;
				text-align: center;
				font-size: 12px;
				margin-top: 5px;
			}
		}

		&__show-more {
			background-color: #fff0f0;
			border-radius: 3px;
			padding: 3px 6px;
			@include flex(column);
			align-items: center;

			&__text {
				font-size: 12px;
				width: 12px;
				color: #f56c6c;
				line-height: 16px;
			}
		}
	}

	.index-content {
		min-height: 90vh;
		background: #f9f9f9;
	}

	.index-serach {
		position: sticky;
		background-color: #0090d0;
		padding: 0 30rpx;
		height: 60px;
		display: flex;
		align-items: center;
		z-index: 11;

		.u-search__content {
			box-shadow: 0px 0px 3px 1px rgba(255, 255, 255, 0.3);
		}

		.u-search__content__input--placeholder {
			color: #FFF;
		}
	}

	.index-swiper {
		padding: 30rpx;
	}

	.index-tabs {
		overflow: scroll;
		position: sticky;
		// padding-top: 24rpx;
		padding-left: 30rpx;
		background: #FFFFFF;
		z-index: 11;

		&-one {
			display: inline-block;
			border-radius: 24rpx;
			background: #f1f1f1;
			color: #999;
			font-size: 28rpx;
			margin-right: 20rpx;
			padding: 10rpx 20rpx;
		}

		&-active {
			background: #0090d0;
			color: #FFFFFF;
		}
	}

	.index-block {
		padding: 40rpx;
		background-color: #fff;
	}

	.index-block-title {
		font-size: 40rpx;
		font-weight: bold;
		padding: 0 0 40rpx 0;
	}

	.item-tj {
		width: 100%;
		height: 160rpx;
		border-radius: 16rpx;

		&-frist {
			background-color: rgba($color: #2979ff, $alpha: 0.8);
			// background-image: url('/static/img/bg/qb.png');
		}

		&-second {
			background-color: rgba($color: #303133, $alpha: 0.8);
			// background-image: url('/static/img/bg/qb.png');
		}

		&-thrid {
			background-color: rgba($color: #19be6b, $alpha: 0.8);
			// background-image: url('/static/img/bg/qb.png');
		}
	}

	// AI食物识别悬浮按钮样式
	.ai-float-button {
		position: fixed;
		right: 30rpx;
		bottom: 200rpx;
		z-index: 999;

		.float-button-inner {
			width: 120rpx;
			height: 120rpx;
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			border-radius: 60rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.4);
			animation: float 3s ease-in-out infinite;

			.float-button-text {
				color: #FFFFFF;
				font-size: 20rpx;
				margin-top: 6rpx;
				font-weight: bold;
			}
		}

		&:active .float-button-inner {
			transform: scale(0.95);
			transition: transform 0.1s;
		}
	}

	// 悬浮动画
	@keyframes float {

		0%,
		100% {
			transform: translateY(0rpx);
		}

		50% {
			transform: translateY(-10rpx);
		}
	}

	// AI智能分析报告模块样式
	.ai-analysis-module {
		margin: 0 25rpx 15rpx 25rpx;
		padding: 12rpx 20rpx;
		background: linear-gradient(to right, #667eea, #764ba2);
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		color: #fff;
		font-weight: bold;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06), 0 1rpx 4rpx rgba(0, 0, 0, 0.03);
		transition: all 0.3s ease;
		height: 65rpx;
		position: relative;

		&:active {
			transform: translateY(1rpx);
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04), 0 1rpx 2rpx rgba(0, 0, 0, 0.02);
		}

		.ai-content {
			flex: 1;
			display: flex;
			align-items: center;
			padding-left: 40rpx;

			.ai-title {
				font-size: 30rpx;
				line-height: 1;
			}
		}

		.ai-icon {
			position: absolute;
			right: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.tabs-sticky-container {
		position: sticky;
		z-index: 50;
		background: #fff;
		width: 100%;
	}

	.custom-tabs-scroll {
		width: 710rpx;
		overflow-x: scroll;
		white-space: nowrap;
		// background: #fff;
		margin: 0 20rpx;
	}

	.custom-tabs {
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.custom-tabs-spacer {
		width: 20rpx;
		height: 1rpx;
		flex-shrink: 0;
		/* 防止被压缩 */
	}

	.custom-tab-item {
		display: inline-block;
		padding: 10rpx 24rpx;
		margin-right: 15rpx;
		margin-bottom: 10rpx;
		border-radius: 32rpx;
		background: #f5f5f8;
		color: #666;
		font-size: 26rpx;
		transition: background 0.2s, color 0.2s;
	}

	.custom-tab-active {
		background: linear-gradient(90deg, #b7bbfa 0%, #e3e6fa 100%);
		color: #6c4fd3;
		font-weight: bold;
		box-shadow: 0 2rpx 8rpx rgba(183, 187, 250, 0.12);
	}
</style>
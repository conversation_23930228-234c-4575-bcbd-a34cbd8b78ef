<template>
  <div class="intervene-edit-wrapper">
    <!-- 顶部操作栏 -->
    <div class="edit-header">
      <div class="header-title">
        <h3>干预方案编辑</h3>
        <span v-if="interveneRow">患者：{{ interveneRow.patientName }}</span>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="handleSave" :loading="saving">
          确定保存
        </el-button>
        <el-button @click="handleCancel">
          取消
        </el-button>
      </div>
    </div>

    <!-- 干预方案编辑内容 -->
    <div class="edit-content">
      <intervene-edit 
        v-if="interveneId" 
        :intervene-id="interveneId"
        :is-dialog-mode="true"
        ref="interveneEditRef"
        @save-success="handleSaveSuccess"
      />
    </div>
  </div>
</template>

<script>
import InterveneEdit from './interveneEdit.vue'

export default {
  name: 'InterveneEditComponent',
  components: {
    InterveneEdit
  },
  props: {
    interveneId: {
      type: [String, Number],
      required: true
    },
    interveneRow: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      saving: false
    }
  },
  methods: {
    // 处理保存
    async handleSave() {
      try {
        this.saving = true;
        // 调用子组件的保存方法
        if (this.$refs.interveneEditRef && this.$refs.interveneEditRef.handleSave) {
          await this.$refs.interveneEditRef.handleSave();
        } else {
          // 如果子组件没有handleSave方法，触发保存成功事件
          this.handleSaveSuccess();
        }
      } catch (error) {
        console.error('保存失败:', error);
        this.$modal.msgError("保存失败");
      } finally {
        this.saving = false;
      }
    },
    
    // 处理取消
    handleCancel() {
      this.$emit('cancel');
    },
    
    // 处理保存成功
    handleSaveSuccess() {
      this.saving = false;
      this.$emit('save-success');
    }
  }
}
</script>

<style scoped>
.intervene-edit-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.header-title h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
}

.header-title span {
  color: #666;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.edit-content {
  flex: 1;
  overflow: hidden;
  padding: 20px;
}

/* 确保内容区域可以滚动 */
.edit-content >>> .intervene-edit-container {
  height: 100%;
  overflow-y: auto;
}
</style>

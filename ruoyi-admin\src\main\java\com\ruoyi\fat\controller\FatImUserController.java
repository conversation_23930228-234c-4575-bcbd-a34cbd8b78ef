package com.ruoyi.fat.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.util.Date;

import com.mchange.v1.identicator.IdHashMap;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.CaszUnit;
import com.ruoyi.fat.domain.VoiceAssistantResponse;
import com.ruoyi.fat.service.impl.VoiceAssistantService;
import com.ruoyi.im.domain.YeMessage;
import com.ruoyi.im.service.YeImService;
import com.ruoyi.system.service.ICaszUnitService;
import jakarta.servlet.http.HttpServletResponse;

import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.fat.domain.FatImUnit;
import com.ruoyi.fat.domain.FatPatientDoctor;
import com.ruoyi.fat.service.IFatImUnitService;
import com.ruoyi.fat.service.IFatPatientDoctorService;
import com.ruoyi.im.domain.AIMessage;
import com.ruoyi.im.service.ImService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.web.domain.*;

import com.ruoyi.web.service.IAssistantRecordService;
import com.ruoyi.web.service.ICaszExamResultService;
import com.ruoyi.web.service.IDailyReminderLogService;
import com.ruoyi.web.service.IImProhibitService;
import com.ruoyi.web.utils.AIUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fat.domain.FatImUser;
import com.ruoyi.fat.service.IFatImUserService;
import com.ruoyi.fat.domain.FatPatientDoctor;
import com.ruoyi.fat.service.IFatPatientDoctorService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * IM通讯录Controller
 *
 * <AUTHOR>
 * @date 2024-09-07
 */
@RestController
@RequestMapping("/exchange/imUser")
public class FatImUserController extends BaseController {
    @Autowired
    private IFatImUserService fatImUserService;

    @Autowired
    private ICaszExamResultService caszExamResultService;
    @Autowired
    private ImService imService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IFatPatientDoctorService fatPatientDoctorService;
    @Autowired
    private IFatImUnitService fatImUnitService;

    @Autowired
    private IDailyReminderLogService dailyReminderLogService;

    @Autowired
    private IAssistantRecordService assistantRecordService;

    @Autowired
    private IImProhibitService imProhibitService;

    @Autowired
    AIUtils aiUtils;
    @Autowired
    private ICaszUnitService unitService;
    @Autowired
    private YeImService yeImService;

    @Autowired
    private VoiceAssistantService voiceAssistantService;

    @Autowired
    RedisTemplate redisTemplate;

    /**
     * 查询IM通讯录列表
     */
    @PreAuthorize("@ss.hasPermi('exchange:imUser:list')")
    @GetMapping("/list")
    public TableDataInfo list(FatImUser fatImUser) {
        startPage();
        List<FatImUser> list = fatImUserService.selectFatImUserList(fatImUser);
        return getDataTable(list);
    }

    /**
     * 小程序查询IM通讯录列表 校验是否为机构
     */

    @GetMapping("/unitList")
    public TableDataInfo unitList(FatImUser fatImUser) {
        startPage();
        if (fatImUser.getUserId() == "") {
            fatImUser.setUserId("999999");
        }
        List<FatImUser> list = fatImUserService.selectImUserUnitList(fatImUser);
        return getDataTable(list);
    }

    /**
     *
     * 根据小程序会话id查询机构的ai开启状态
     *
     * @param fatImUser
     * @return
     */
    /**
     * 获取当前用户的聊天用户ID
     */
    @GetMapping("/getChatUserId")
    public AjaxResult getChatUserId() {
        try {
            // 获取当前登录用户的系统用户ID
            String currentSysUserId = getUserId().toString();

            // 查询当前用户的IM用户信息
            LambdaQueryWrapper<FatImUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatImUser::getSysUserId, currentSysUserId);
            wrapper.eq(BaseEntity::getCreateBy,"yeim");
            FatImUser imUser = fatImUserService.getOne(wrapper);

            if (imUser != null) {
                Map<String, String> result = new HashMap<>();
                result.put("chatUserId", imUser.getUserId());
                result.put("sysUserId", currentSysUserId);
                return success(result);
            } else {
                return error("当前用户未注册IM账号");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return error("获取聊天用户ID失败: " + e.getMessage());
        }
    }

    @GetMapping("/unitAiStatus")
    public AjaxResult unitAiStatus(FatImUser fatImUser) {
        Map<String, Boolean> map = new HashMap<>();
        map.put("isHos", false);
        map.put("aiOpen", false);
        LambdaQueryWrapper<FatImUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatImUser::getUserId, fatImUser.getUserId());
        FatImUser imUser = fatImUserService.getOne(wrapper);
        if (imUser != null) {
            String ex = imUser.getEx();
            if ("yehos".equals(ex)) {
                map.put("isHos", true);
                // 查询机构的ai开启状态
                LambdaQueryWrapper<CaszUnit> unitWrapper = new LambdaQueryWrapper<>();
                unitWrapper.eq(CaszUnit::getId, imUser.getUnitId());
                CaszUnit unit = unitService.getOne(unitWrapper);
                if (unit != null) {
                    String aiStatus = unit.getAiStatus();
                    if ("true".equals(aiStatus)) {
                        map.put("aiOpen", true);
                    } else {
                        map.put("aiOpen", false);
                    }
                }
            }
        }
        return success(map);
    }

    /**
     * AI机构回复
     */

    @PostMapping("/reply")
    public AjaxResult reply(@RequestBody AIMessage aiMessage) {
        System.out.println(aiMessage);
        System.out.println("患者的ID是" + aiMessage.getPatientId());
        Map<String, Object> textElem = (Map<String, Object>) aiMessage.getTextElem();
        String reply = textElem.get("content").toString();
        String notificationName = "AI回复";
        // 被禁言
        ImProhibit jinProhibit = new ImProhibit();
        jinProhibit.setProhibitTime(new Date());
        List<ImProhibit> imProhibitList = imProhibitService.selectImProhibitList(jinProhibit);
        if (imProhibitList.size() >= 1) {
            imService.sendAiMessage(aiMessage.getRecvID(), aiMessage.getSendID(),
                    aiMessage.getGroupID(), "抱歉，您违反社区规定次数过多，本功能暂停使用", aiMessage.getContentType(),
                    aiMessage.getSessionType(),
                    notificationName, aiMessage.getShowName(), aiMessage.getSenderFaceURL(), null);
            return success();
        }
        // 用尽次数
        AssistantRecord assistantRecordToday = new AssistantRecord();
        assistantRecordToday.setSendId(aiMessage.getSendID());
        assistantRecordToday.setCreateTime(new Date());
        List<AssistantRecord> RecordTodaylist = assistantRecordService.selectTodayList(assistantRecordToday);
        if (RecordTodaylist.size() >= 30) {
            imService.sendAiMessage(aiMessage.getRecvID(), aiMessage.getSendID(),
                    aiMessage.getGroupID(), "抱歉，您今日的提问次数已用尽", aiMessage.getContentType(), aiMessage.getSessionType(),
                    notificationName, aiMessage.getShowName(), aiMessage.getSenderFaceURL(), null);
            return success();
        }

        try {
            // 获取AI回复
            AssistantRecord multiwheel = new AssistantRecord();
            multiwheel.setSendId(aiMessage.getSendID());
            multiwheel.setRecvId(aiMessage.getRecvID());
            // 查询历史聊天记录 3对
            List<AssistantRecord> multiwheelList = assistantRecordService.selectmultiwheelList(multiwheel);

            // String content = AIUtils.callWithMessage(multiwheelList,reply);
            String content = AIUtils.callWithFunctionCall(multiwheelList, reply, aiMessage.getPatientId());
            // String content =
            // aiUtils.selectToolMessage(multiwheelList,reply,aiMessage.getPatientId(),null
            // != aiMessage.getUnitId() ? aiMessage.getUnitId() : "");
            content = content.replace("\n", " <br>");
            System.out.println("ai:" + content);

            // 记录每一次的问答
            AssistantRecord assistantRecord = new AssistantRecord();
            assistantRecord.setSendId(aiMessage.getSendID());
            assistantRecord.setRecvId(aiMessage.getRecvID());
            assistantRecord.setProblem(reply);
            assistantRecord.setAnswer(content);
            assistantRecord.setCreateTime(new Date());
            if (content.contains("无法回答您")) {
                assistantRecord.setViolation("1");
            }
            assistantRecordService.save(assistantRecord);

            AssistantRecord ass = new AssistantRecord();
            ass.setViolation("1");
            ass.setSendId(aiMessage.getSendID());
            List<AssistantRecord> AssistantRecordList = assistantRecordService.selectAssistantRecordList(ass);

            ImProhibit imProhibit = new ImProhibit();
            // 第一次违反
            if (AssistantRecordList.size() == 3) {

                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DAY_OF_YEAR, 1);
                Date newDate = calendar.getTime();
                imProhibit.setImId(aiMessage.getSendID());
                imProhibit.setProhibitTime(newDate);
                imProhibitService.save(imProhibit);
                imService.sendAiMessage(aiMessage.getRecvID(), aiMessage.getSendID(),
                        aiMessage.getGroupID(), "抱歉，您违反社区规定次数过多，本功能暂停使用", aiMessage.getContentType(),
                        aiMessage.getSessionType(),
                        notificationName, aiMessage.getShowName(), aiMessage.getSenderFaceURL(), null);
            } else if (AssistantRecordList.size() == 2) {
                imService.sendAiMessage(aiMessage.getRecvID(), aiMessage.getSendID(),
                        aiMessage.getGroupID(), "您询问的问题似乎不在减重与代谢健康管理的范围内呢，如果再次跑题，那我只能明天再为您提供服务了",
                        aiMessage.getContentType(), aiMessage.getSessionType(),
                        notificationName, aiMessage.getShowName(), aiMessage.getSenderFaceURL(), null);
            } else if (AssistantRecordList.size() == 4) {
                imService.sendAiMessage(aiMessage.getRecvID(), aiMessage.getSendID(),
                        aiMessage.getGroupID(), "您询问的问题似乎不在减重与代谢健康管理的范围内呢，如果再次跑题，我将暂停为您提供服务",
                        aiMessage.getContentType(), aiMessage.getSessionType(),
                        notificationName, aiMessage.getShowName(), aiMessage.getSenderFaceURL(), null);
            } else if (AssistantRecordList.size() > 5) {
                imService.sendAiMessage(aiMessage.getRecvID(), aiMessage.getSendID(),
                        aiMessage.getGroupID(), "您询问的问题不在减重与代谢健康管理的范围内的次数过多，本功能暂停使用", aiMessage.getContentType(),
                        aiMessage.getSessionType(),
                        notificationName, aiMessage.getShowName(), aiMessage.getSenderFaceURL(), null);
            } else {
                imService.sendAiMessage(aiMessage.getRecvID(), aiMessage.getSendID(),
                        aiMessage.getGroupID(), content, aiMessage.getContentType(), aiMessage.getSessionType(),
                        notificationName, aiMessage.getShowName(), aiMessage.getSenderFaceURL(), null);
            }
        } catch (NoApiKeyException e) {
            e.printStackTrace();
        } catch (InputRequiredException e) {
            e.printStackTrace();
        }
        return success();
    }

    @PostMapping("/yeAiReply")
    public AjaxResult yeAiReply(@RequestBody YeMessage aiMessage) {
        System.out.println(aiMessage);
        String type = aiMessage.getType();
        Object body = aiMessage.getBody();
        JSONObject msgBody = JSONObject.from(body);
        String patientId = msgBody.getString("patientId");
        String unitId = msgBody.getString("unitId");
        String roleTips = msgBody.getString("roleTips");//ai 人设
        String reply = null;
        if ("text".equals(type)) {
            reply = msgBody.getString("text");
        } else if ("audio".equals(type)) {
            String audioUrl = msgBody.getString("audioUrl");
            if (StringUtils.isNotEmpty(audioUrl)) {
                reply = fatImUserService.changeVoiceToText(audioUrl);
            } else {
                AjaxResult.error("内容是空的");
            }
        }

        //意图分析
//        String intent = aiUtils.selectIntent(reply);
//        System.out.println("意图："+intent);

        // 被禁言
//        ImProhibit jinProhibit = new ImProhibit();
//        jinProhibit.setProhibitTime(new Date());
//        List<ImProhibit> imProhibitList = imProhibitService.selectImProhibitList(jinProhibit);
//        if (!imProhibitList.isEmpty()) {
//            yeImService.sendMessage(aiMessage.getTo(), aiMessage.getFrom(), null, "抱歉，您违反社区规定次数过多，本功能暂停使用");
//            return success();
//        }
        // //用尽次数
        AssistantRecord assistantRecordToday = new AssistantRecord();
        assistantRecordToday.setSendId(aiMessage.getFrom());
        assistantRecordToday.setCreateTime(new Date());
        List<AssistantRecord> RecordTodaylist = assistantRecordService.selectTodayList(assistantRecordToday);
        if (RecordTodaylist.size() >= 50) {
            yeImService.sendMessage(aiMessage.getTo(), aiMessage.getFrom(), null, "抱歉，您今日的提问次数已用尽");
            return success();
        }
        try {
            // 获取AI回复
            AssistantRecord multiwheel = new AssistantRecord();
            multiwheel.setSendId(aiMessage.getFrom());
            multiwheel.setRecvId(aiMessage.getTo());
            // 查询历史聊天记录
            if (null != redisTemplate.opsForValue().get("ai_prompt_time:" + patientId)){
                Date storedDate = (Date) redisTemplate.opsForValue().get("ai_prompt_time:" + patientId);
                multiwheel.setCreateTime(storedDate);
            }

            List<AssistantRecord> multiwheelList = assistantRecordService.selectmultiwheelList(multiwheel);
            // String content = AIUtils.callWithMessage(multiwheelList,reply);
            // String content =
            // AIUtils.callWithFunctionCall(multiwheelList,reply,aiMessage.getPatientId());
            System.out.println("开始执行AI:" + new Date());
            String content = aiUtils.selectToolMessage(multiwheelList, reply, patientId, null != unitId ? unitId : "",roleTips);
            content = content.replace("\n", " <br>");
            System.out.println(new Date() + "执行结束ai:" + content);
            // 记录每一次的问答
            AssistantRecord assistantRecord = new AssistantRecord();
            assistantRecord.setSendId(aiMessage.getFrom());
            assistantRecord.setRecvId(aiMessage.getTo());
            assistantRecord.setProblem(reply);
            assistantRecord.setAnswer(content);
            assistantRecord.setCreateTime(new Date());
            if (content.contains("无法回答您")) {
                assistantRecord.setViolation("1");
            }
            assistantRecordService.save(assistantRecord);
            AssistantRecord ass = new AssistantRecord();
            ass.setViolation("1");
            ass.setSendId(aiMessage.getFrom());
            List<AssistantRecord> AssistantRecordList = assistantRecordService.selectAssistantRecordList(ass);
            ImProhibit imProhibit = new ImProhibit();
            // 第一次违反
            if (AssistantRecordList.size() == 3) {
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DAY_OF_YEAR, 1);
                Date newDate = calendar.getTime();
                imProhibit.setImId(aiMessage.getFrom());
                imProhibit.setProhibitTime(newDate);
                imProhibitService.save(imProhibit);
                yeImService.sendMessage(aiMessage.getTo(), aiMessage.getFrom(),
                        null, "抱歉，您违反社区规定次数过多，本功能暂停使用");
            } else if (AssistantRecordList.size() == 2) {
                yeImService.sendMessage(aiMessage.getTo(), aiMessage.getFrom(),
                        null, "您询问的问题似乎不在减重与代谢健康管理的范围内呢，如果再次跑题，那我只能明天再为您提供服务了");
            } else if (AssistantRecordList.size() == 4) {
                yeImService.sendMessage(aiMessage.getTo(), aiMessage.getFrom(),
                        null, "您询问的问题似乎不在减重与代谢健康管理的范围内呢，如果再次跑题，我将暂停为您提供服务");
            } else if (AssistantRecordList.size() > 5) {
                yeImService.sendMessage(aiMessage.getTo(), aiMessage.getFrom(),
                        null, "您询问的问题不在减重与代谢健康管理的范围内的次数过多，本功能暂停使用");
            } else {
                yeImService.sendMessage(aiMessage.getTo(), aiMessage.getFrom(),
                        null, content);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return success();
    }

    // 方法作废
    @Deprecated
    @PostMapping("/replyAi")
    public AjaxResult replyAi(@RequestBody AIMessage aiMessage) {
        System.out.println(aiMessage);
        System.out.println("患者的ID是" + aiMessage.getPatientId());
        Map<String, Object> textElem = (Map<String, Object>) aiMessage.getTextElem();
        String reply = "";
        if (null != textElem) {
            reply = textElem.get("content").toString();
        } else {
            // 说明是语音回复
            if (null != aiMessage.getUrl()) {
                reply = fatImUserService.changeVoiceToText(aiMessage.getUrl());
            } else {
                AjaxResult.error("内容是空的");
            }

        }
        String notificationName = "AI回复";
        // 被禁言
        ImProhibit jinProhibit = new ImProhibit();
        jinProhibit.setProhibitTime(new Date());
        List<ImProhibit> imProhibitList = imProhibitService.selectImProhibitList(jinProhibit);
        if (imProhibitList.size() >= 1) {
            imService.sendAiMessage(aiMessage.getRecvID(), aiMessage.getSendID(),
                    aiMessage.getGroupID(), "抱歉，您违反社区规定次数过多，本功能暂停使用", aiMessage.getContentType(),
                    aiMessage.getSessionType(),
                    notificationName, aiMessage.getShowName(), aiMessage.getSenderFaceURL(), null);
            return success();
        }
        // 用尽次数
        AssistantRecord assistantRecordToday = new AssistantRecord();
        assistantRecordToday.setSendId(aiMessage.getSendID());
        assistantRecordToday.setCreateTime(new Date());
        List<AssistantRecord> RecordTodaylist = assistantRecordService.selectTodayList(assistantRecordToday);
        if (RecordTodaylist.size() >= 30) {
            imService.sendAiMessage(aiMessage.getRecvID(), aiMessage.getSendID(),
                    aiMessage.getGroupID(), "抱歉，您今日的提问次数已用尽", aiMessage.getContentType(), aiMessage.getSessionType(),
                    notificationName, aiMessage.getShowName(), aiMessage.getSenderFaceURL(), null);
            return success();
        }

        try {
            // 获取AI回复
            AssistantRecord multiwheel = new AssistantRecord();
            multiwheel.setSendId(aiMessage.getSendID());
            multiwheel.setRecvId(aiMessage.getRecvID());
            // 查询历史聊天记录 3对
            List<AssistantRecord> multiwheelList = assistantRecordService.selectmultiwheelList(multiwheel);

            // String content = AIUtils.callWithMessage(multiwheelList,reply);
            // String content =
            // AIUtils.callWithFunctionCall(multiwheelList,reply,aiMessage.getPatientId());
            System.out.println("开始执行AI:" + new Date());
            String content = aiUtils.selectToolMessage(multiwheelList, reply, aiMessage.getPatientId(),
                    null != aiMessage.getUnitId() ? aiMessage.getUnitId() : "","");
            content = content.replace("\n", " <br>");
            System.out.println(new Date() + "执行结束ai:" + content);

            // 记录每一次的问答
            AssistantRecord assistantRecord = new AssistantRecord();
            assistantRecord.setSendId(aiMessage.getSendID());
            assistantRecord.setRecvId(aiMessage.getRecvID());
            assistantRecord.setProblem(reply);
            assistantRecord.setAnswer(content);
            assistantRecord.setCreateTime(new Date());
            if (content.contains("无法回答您")) {
                assistantRecord.setViolation("1");
            }
            assistantRecordService.save(assistantRecord);

            AssistantRecord ass = new AssistantRecord();
            ass.setViolation("1");
            ass.setSendId(aiMessage.getSendID());
            List<AssistantRecord> AssistantRecordList = assistantRecordService.selectAssistantRecordList(ass);

            ImProhibit imProhibit = new ImProhibit();
            // 第一次违反
            if (AssistantRecordList.size() == 3) {

                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DAY_OF_YEAR, 1);
                Date newDate = calendar.getTime();
                imProhibit.setImId(aiMessage.getSendID());
                imProhibit.setProhibitTime(newDate);
                imProhibitService.save(imProhibit);
                imService.sendAiMessage(aiMessage.getRecvID(), aiMessage.getSendID(),
                        aiMessage.getGroupID(), "抱歉，您违反社区规定次数过多，本功能暂停使用", aiMessage.getContentType(),
                        aiMessage.getSessionType(),
                        notificationName, aiMessage.getShowName(), aiMessage.getSenderFaceURL(), null);
            } else if (AssistantRecordList.size() == 2) {
                imService.sendAiMessage(aiMessage.getRecvID(), aiMessage.getSendID(),
                        aiMessage.getGroupID(), "您询问的问题似乎不在减重与代谢健康管理的范围内呢，如果再次跑题，那我只能明天再为您提供服务了",
                        aiMessage.getContentType(), aiMessage.getSessionType(),
                        notificationName, aiMessage.getShowName(), aiMessage.getSenderFaceURL(), null);
            } else if (AssistantRecordList.size() == 4) {
                imService.sendAiMessage(aiMessage.getRecvID(), aiMessage.getSendID(),
                        aiMessage.getGroupID(), "您询问的问题似乎不在减重与代谢健康管理的范围内呢，如果再次跑题，我将暂停为您提供服务",
                        aiMessage.getContentType(), aiMessage.getSessionType(),
                        notificationName, aiMessage.getShowName(), aiMessage.getSenderFaceURL(), null);
            } else if (AssistantRecordList.size() > 5) {
                imService.sendAiMessage(aiMessage.getRecvID(), aiMessage.getSendID(),
                        aiMessage.getGroupID(), "您询问的问题不在减重与代谢健康管理的范围内的次数过多，本功能暂停使用", aiMessage.getContentType(),
                        aiMessage.getSessionType(),
                        notificationName, aiMessage.getShowName(), aiMessage.getSenderFaceURL(), null);
            } else {
                imService.sendAiMessage(aiMessage.getRecvID(), aiMessage.getSendID(),
                        aiMessage.getGroupID(), content, aiMessage.getContentType(), aiMessage.getSessionType(),
                        notificationName, aiMessage.getShowName(), aiMessage.getSenderFaceURL(), aiMessage.getUrl());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return success();
    }

    /**
     * AI系统每日提醒
     */

    @PostMapping("/daily")
    public AjaxResult daily(@RequestBody AIMessage aiMessage) {
        String content = "您好，我是您的数智小助手，您可以随时询问我关于减重及代谢健康、生活方式及营养、运动方面的建议哦。";
        String notificationName = "AI提醒";
        imService.sendAiMessage(aiMessage.getSendID(), aiMessage.getRecvID(),
                "", content, 101, 1,
                notificationName, aiMessage.getShowName(), aiMessage.getSenderFaceURL(), null);
        System.out.println("头像" + aiMessage.getSenderFaceURL());

        DailyReminderLog dailyReminderLog = new DailyReminderLog();
        dailyReminderLog.setSendId(aiMessage.getSendID());
        dailyReminderLog.setRecvId(aiMessage.getRecvID());
        dailyReminderLog.setCreateTime(new Date());
        dailyReminderLogService.save(dailyReminderLog);
        return success();
    }

    /**
     * 制定干预计划
     */

    @PostMapping("/CreateScheme")
    public AjaxResult CreateScheme(@RequestBody Scheme scheme) {
        String text = "";
        String zhibiao = "";
        String wenjuan = "";
        String gender = "";
        CaszExamResult caszExamResult = caszExamResultService.selectCaszExamResultByPatientId(scheme.getPatientId());
        if (caszExamResult != null) {
            wenjuan = "详细信息如下：" + caszExamResult.getTextValue() + "  ";
        }

        JSONObject intervention = new JSONObject();

        String genderStr = "";
        if (scheme.getGender() != null) {
            if (scheme.getGender().equals("0")) {
                gender += "女";
            } else if (scheme.getGender().equals("1")) {
                gender += "男";
            } else {
                gender += "未知";
            }
            genderStr = "性别" + gender;
        }

        String heightStr = "";
        if (scheme.getHeight() != null) {
            heightStr = "身高" + scheme.getHeight() + "cm";
        }

        String weightStr = "";
        if (scheme.getWeight() != null) {
            weightStr = "体重" + scheme.getWeight() + "kg";
        }

        String waistStr = "";
        if (scheme.getWaist() != null) {
            waistStr = "腰围" + scheme.getWaist() + "cm";
        }

        String expectedDietaryPlanStr = "";
        if (scheme.getExpectedDietaryPlan() != null) {
            expectedDietaryPlanStr = "期望的饮食方案：" + scheme.getExpectedDietaryPlan();
        }

        String drugStr = "";
        if (scheme.getDrug() != null) {
            drugStr = "当前服用的药物" + scheme.getDrug();
        }

        String hopeHOTStr = "";
        if (scheme.getHopeHOT() != null) {
            hopeHOTStr = "每日饮食摄入能量建议在" + scheme.getHopeHOT() + "以下  ";
        }

        String dateRange = "";
        if (scheme.getDrug() != null) {
            dateRange = "计划的起止时间" + scheme.getDrug();
        }

        text = "患者基本信息如下：" + genderStr + "，" + heightStr + "，" + weightStr + "，" + waistStr + "，" +
                expectedDietaryPlanStr + dateRange + drugStr + "，" + hopeHOTStr;

        String test = "你是一个专业的内分泌和营养科医生，请依照以下纲要，帮我生成一份面向患者的详细减重干预方案，请用患者看的懂的表述方式认真整理，任何一个摘要点都不可以几句就概括掉，以JSON格式输出，输出的JSON需遵守以下的格式，文本需要换行可以在json数据中加入换行符：\\n"
                +
                "\"InterventionPlan\":{" +
                "\"LifestyleIntervention\": \"（1）健康宣教：\n说明肥胖病理机制与健康风险，澄清科学减重误区（如节食反弹、局部减脂迷思）；（2）昼夜节律管理：\n睡眠计划（保障22:00 - 6:00优质睡眠），光暴露调整（晨间日光调节生物钟）\",\n"
                +
                "\"DietPlan\": \"（1）膳食设计与营养平衡：\n给出科学合理膳食建议（符合南方饮食习惯）、食物搭配和三日详细轮换餐单、注意事项、低GI食物清单与替代方案（如糙米替代白米）；（2）场景化饮食指导：\n外食选择策略（火锅/快餐避坑指南）、节假日/聚餐应对技巧（餐前饮水、优先蛋白质）、特殊时期调整（孕期热量微调、更年期控糖）；（3）饮水与进食行为：\n每日饮水量（体重kg×30ml + 运动额外补充）、正念饮食训练（慢咀嚼、无电子设备进食）\",\n"
                +
                "\"ScientificExercisePlan\": \"（1）运动处方：\n有氧运动每周150分钟中强度（如快走、游泳），抗阻训练每周2 - 3次全身肌群训练（深蹲、弹力带），碎片运动每小时久坐后3分钟微运动（靠墙静蹲、踮脚）；（2）安全防护：\nBMI＞28者运动禁忌（避免跳跃、推荐水中运动）、运动损伤应急处理（RICE原则）；（3）运动前后注意事项：\n\",\n"
                +
                "\"BehaviorManagement\": \"（1）认知行为疗法（CBT）：\n记录“饮食 - 情绪 - 行为”日记、破除非理性信念（如“必须吃完所有食物”）；（2）压力与情绪管理：\n皮质醇调控（深呼吸（4 - 7 - 8呼吸法）、冥想）、替代性奖励机制（运动替代情绪性进食）；（3）阶段性反馈与激励：\n非体重进展奖励（如腰围缩小、睡眠改善）\",\n"
                +
                "\"OtherPrecautions\": \"（1）营养补充与代餐管理:\n（2）平台期突破策略\n；（3）长期维持与防反弹：\n\"\n" +
                "} \n" +
                text + wenjuan +
                "患者基本医学实验室指标如下：\n";
        try {

            String content = AIUtils.CreateScheme(test);
            String cleanedReply = content.replace("```json", "").replace("```", "").trim();

            JSONObject jsonObject = JSON.parseObject(cleanedReply);

            String intervention_plan = jsonObject.getString("InterventionPlan");

            intervention = JSON.parseObject(intervention_plan);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return success(intervention);

    }

    /**
     * 获取聊天历史记录
     */
    @GetMapping("/chatHistory")
    public AjaxResult getChatHistory(@RequestParam("conversationId") String conversationId,
            @RequestParam(value = "nextMessageId", required = false) String nextMessageId) {
        try {
            // 获取当前登录用户信息
            String currentUserId = getUserId().toString();

            // 查询当前用户的IM用户信息
            LambdaQueryWrapper<FatImUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatImUser::getSysUserId, currentUserId);
            wrapper.eq(BaseEntity::getCreateBy,"yeim");
            FatImUser currentImUser = fatImUserService.getOne(wrapper);

            if (currentImUser == null) {
                return error("当前用户未注册IM账号");
            }

            // 调用YeImService获取聊天历史记录
            JSONArray chatHistory = yeImService.getChatHistory(conversationId, nextMessageId,
                    currentImUser.getUserId());

            // 处理群聊系统通知消息
            if (chatHistory != null && !chatHistory.isEmpty()) {
                for (int i = 0; i < chatHistory.size(); i++) {
                    JSONObject message = chatHistory.getJSONObject(i);
                    String messageType = message.getString("type");

                    // 处理群聊系统通知消息
                    if ("group_sys_notice".equals(messageType)) {
                        // 将系统通知转换为用户友好的文本消息
                        JSONObject body = message.getJSONObject("body");
                        if (body != null) {
                            String noticeText = generateGroupNoticeText(message);

                            // 修改消息类型为文本消息
                            message.put("type", "text");

                            // 修改消息体
                            JSONObject newBody = new JSONObject();
                            newBody.put("text", noticeText);
                            message.put("body", newBody);

                            // 标记为系统消息
                            message.put("isSystemMessage", true);
                        }
                    }
                }
            }

            return success(chatHistory);
        } catch (Exception e) {
            e.printStackTrace();
            return error("获取聊天历史记录失败: " + e.getMessage());
        }
    }

    /**
     * 清除会话未读数
     */
    @GetMapping("/clearUnread")
    public AjaxResult clearConversationUnread(@RequestParam("conversationId") String conversationId) {
        try {
            // 获取当前登录用户信息
            String currentUserId = getUserId().toString();

            // 查询当前用户的IM用户信息
            LambdaQueryWrapper<FatImUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatImUser::getSysUserId, currentUserId);
            wrapper.eq(BaseEntity::getCreateBy,"yeim");
            FatImUser currentImUser = fatImUserService.getOne(wrapper);

            if (currentImUser == null) {
                return error("当前用户未注册IM账号");
            }

            // 调用YeImService清除未读数
            Boolean result = yeImService.clearConversationUnread(conversationId, currentImUser.getUserId());

            if (result) {
                return success("清除未读数成功");
            } else {
                return error("清除未读数失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return error("清除未读数失败: " + e.getMessage());
        }
    }

    /**
     * 发送文字消息（支持私聊和群聊）
     */
    @PostMapping("/sendMessage")
    public AjaxResult sendTextMessage(@RequestBody Map<String, Object> request) {
        try {
            String patientId = (String) request.get("patientId");
            String content = (String) request.get("content");

            if (StringUtils.isEmpty(patientId) || StringUtils.isEmpty(content)) {
                return error("患者ID和消息内容不能为空");
            }

            // 获取当前登录用户信息
            String currentUserId = getUserId().toString();

            // 查询当前用户的IM用户信息
            LambdaQueryWrapper<FatImUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatImUser::getSysUserId, currentUserId);
            wrapper.eq(BaseEntity::getCreateBy,"yeim");
            FatImUser currentImUser = fatImUserService.getOne(wrapper);

            if (currentImUser == null) {
                return error("当前用户未注册IM账号");
            }

            JSONObject result;

            // 判断是群聊还是私聊
            if (patientId.startsWith("group_")) {
                // 群聊消息：patientId就是群组ID
                result = yeImService.sendMessage(currentImUser.getUserId(), null, patientId, content);
            } else {
                // 私聊消息：patientId是用户ID，需要查询患者的IM用户信息
                LambdaQueryWrapper<FatImUser> patientWrapper = new LambdaQueryWrapper<>();
                patientWrapper.eq(FatImUser::getUserId, patientId);
                FatImUser patientImUser = fatImUserService.getOne(patientWrapper);

                if (patientImUser == null) {
                    return error("患者未注册IM账号");
                }

                result = yeImService.sendMessage(currentImUser.getUserId(), patientImUser.getUserId(), null, content);
            }

            return success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return error("发送消息失败: " + e.getMessage());
        }
    }

    /**
     * 发送语音消息（支持私聊和群聊）
     */
    @PostMapping("/sendVoiceMessage")
    public AjaxResult sendVoiceMessage(@RequestBody Map<String, Object> request) {
        try {
            String patientId = (String) request.get("patientId");
            String audioUrl = (String) request.get("audioUrl");
            Integer duration = (Integer) request.get("duration");

            if (StringUtils.isEmpty(patientId) || StringUtils.isEmpty(audioUrl)) {
                return error("患者ID和语音URL不能为空");
            }

            // 获取当前登录用户信息
            String currentUserId = getUserId().toString();

            // 查询当前用户的IM用户信息
            LambdaQueryWrapper<FatImUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatImUser::getSysUserId, currentUserId);
            wrapper.eq(BaseEntity::getCreateBy,"yeim");
            FatImUser currentImUser = fatImUserService.getOne(wrapper);

            if (currentImUser == null) {
                return error("当前用户未注册IM账号");
            }

            JSONObject result;

            // 判断是群聊还是私聊
            if (patientId.startsWith("group_")) {
                // 群聊消息：patientId就是群组ID
                result = yeImService.sendVoiceMessage(currentImUser.getUserId(), null, patientId, audioUrl, duration);
            } else {
                // 私聊消息：patientId是用户ID，需要查询患者的IM用户信息
                LambdaQueryWrapper<FatImUser> patientWrapper = new LambdaQueryWrapper<>();
                patientWrapper.eq(FatImUser::getUserId, patientId);
                FatImUser patientImUser = fatImUserService.getOne(patientWrapper);

                if (patientImUser == null) {
                    return error("患者未注册IM账号");
                }

                result = yeImService.sendVoiceMessage(currentImUser.getUserId(), patientImUser.getUserId(), null, audioUrl, duration);
            }

            return success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return error("发送语音消息失败: " + e.getMessage());
        }
    }

    /**
     * 发送图片消息（支持私聊和群聊）
     */
    @PostMapping("/sendImageMessage")
    public AjaxResult sendImageMessage(@RequestBody Map<String, Object> request) {
        try {
            String patientId = (String) request.get("patientId");
            String imageUrl = (String) request.get("imageUrl");
            String content = (String) request.get("content");

            if (StringUtils.isEmpty(patientId) || StringUtils.isEmpty(imageUrl)) {
                return error("患者ID和图片URL不能为空");
            }

            // 获取当前登录用户信息
            String currentUserId = getUserId().toString();

            // 查询当前用户的IM用户信息
            LambdaQueryWrapper<FatImUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatImUser::getSysUserId, currentUserId);
            wrapper.eq(BaseEntity::getCreateBy,"yeim");
            FatImUser currentImUser = fatImUserService.getOne(wrapper);

            if (currentImUser == null) {
                return error("当前用户未注册IM账号");
            }

            JSONObject result;

            // 判断是群聊还是私聊
            if (patientId.startsWith("group_")) {
                // 群聊消息：patientId就是群组ID
                result = yeImService.sendImageMessage(currentImUser.getUserId(), null, patientId, imageUrl, content);
            } else {
                // 私聊消息：patientId是用户ID，需要查询患者的IM用户信息
                LambdaQueryWrapper<FatImUser> patientWrapper = new LambdaQueryWrapper<>();
                patientWrapper.eq(FatImUser::getUserId, patientId);
                FatImUser patientImUser = fatImUserService.getOne(patientWrapper);

                if (patientImUser == null) {
                    return error("患者未注册IM账号");
                }

                result = yeImService.sendImageMessage(currentImUser.getUserId(), patientImUser.getUserId(), null, imageUrl, content);
            }

            return success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return error("发送图片消息失败: " + e.getMessage());
        }
    }

    /**
     * 导出IM通讯录列表
     */
    @PreAuthorize("@ss.hasPermi('exchange:imUser:export')")
    @Log(title = "IM通讯录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FatImUser fatImUser) {
        List<FatImUser> list = fatImUserService.selectFatImUserList(fatImUser);
        ExcelUtil<FatImUser> util = new ExcelUtil<FatImUser>(FatImUser.class);
        util.exportExcel(response, list, "IM通讯录数据");
    }

    /**
     * 获取IM通讯录详细信息
     */
    // @PreAuthorize("@ss.hasPermi('exchange:imUser:query')")
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") String userId) {
        return success(fatImUserService.selectFatImUserByUserId(userId));
    }

    /**
     * 获取好友列表
     */
    @GetMapping(value = "/friend/list")
    public AjaxResult getFriendList(@RequestParam(value = "page", defaultValue = "1") Integer page,
                                   @RequestParam(value = "limit", defaultValue = "20") Integer limit,
                                   @RequestParam(value = "profile", defaultValue = "1") Integer profile) {
        try {
            // 获取当前登录用户信息
            String currentUserId = getUserId().toString();

            // 查询当前用户的IM用户信息
            LambdaQueryWrapper<FatImUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatImUser::getSysUserId, currentUserId);
            wrapper.eq(BaseEntity::getCreateBy,"yeim");
            FatImUser currentImUser = fatImUserService.getOne(wrapper);
            System.out.println("进来了");
            if (currentImUser == null) {
                return error("当前用户未注册IM账号");
            }

            String imUserId = currentImUser.getUserId(); // 获取IM系统中的userId

            // 调用YeImService获取好友列表
            JSONArray friendList = yeImService.getFriendList(imUserId, page, limit, profile);

            if (friendList != null && !friendList.isEmpty()) {
                // 转换为好友信息格式
                List<Map<String, Object>> friendInfoList = new ArrayList<>();

                for (Object item : friendList) {
                    JSONObject friendRecord = (JSONObject) item;

                    Map<String, Object> friendInfo = new HashMap<>();

                    // 从friendRecord中获取基本信息
                    String friendUserId = friendRecord.getString("friendUserId");
                    String userId = friendRecord.getString("userId");
                    friendInfo.put("userId", userId);
                    friendInfo.put("friendUserId", friendUserId);
                    friendInfo.put("createTime", friendRecord.getLong("createAt"));

                    // 使用friendUserId作为主要ID和会话ID
                    friendInfo.put("id", friendUserId);
                    friendInfo.put("imUserId", friendUserId);

                    // 从friendInfo嵌套对象中获取详细信息
                    JSONObject nestedFriendInfo = friendRecord.getJSONObject("friendInfo");
                    if (nestedFriendInfo != null) {
                        String nickname = nestedFriendInfo.getString("nickname");
                        String avatarUrl = nestedFriendInfo.getString("avatarUrl");

                        friendInfo.put("name", nickname != null ? nickname : "");
                        friendInfo.put("avatar", avatarUrl);
                        friendInfo.put("phoneNumber", friendUserId); // 显示好友ID在昵称下面

                        // 如果profile=1，包含更多详细资料
                        if (profile == 1) {
                            friendInfo.put("gender", nestedFriendInfo.getString("gender"));
                            friendInfo.put("birth", nestedFriendInfo.getString("birth"));
                            friendInfo.put("email", nestedFriendInfo.getString("email"));
                        }
                    } else {
                        // 如果没有friendInfo嵌套对象，使用默认值
                        friendInfo.put("name", "未知好友");
                        friendInfo.put("avatar", null);
                        friendInfo.put("phoneNumber", userId);
                    }

                    // 添加其他必要字段
                    friendInfo.put("chatGroupId", "");
                    friendInfo.put("lastMessage", "");
                    friendInfo.put("lastMessageTime", null);
                    friendInfo.put("unreadCount", 0);

                    friendInfoList.add(friendInfo);
                }

                return success(friendInfoList);
            } else {
                return success(new ArrayList<>());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return error("获取好友列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取会话列表
     */
    @GetMapping(value = "/doctorPatients/{userId}")
    public AjaxResult getDoctorPatients(@PathVariable("userId") String userId) {
        try {
            // 先通过sysUserId查询FatImUser，获取IM系统中的userId
            LambdaQueryWrapper<FatImUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatImUser::getSysUserId, userId);
            wrapper.eq(BaseEntity::getCreateBy,"yeim");
            FatImUser fatImUser = fatImUserService.getOne(wrapper);

            if (fatImUser == null) {
                return error("未找到对应的IM用户信息");
            }

            String imUserId = fatImUser.getUserId(); // 获取IM系统中的userId

            // 调用YeImService获取会话列表
            JSONArray conversationList = yeImService.getConversationList(imUserId, 1, 20);

            if (conversationList != null && !conversationList.isEmpty()) {
                // 转换为患者聊天信息格式
                List<Map<String, Object>> patientList = new ArrayList<>();

                for (Object item : conversationList) {
                    JSONObject conversationRecord = (JSONObject) item;

                    // 获取records数组，实际的会话数据在这里面
                    JSONArray records = conversationRecord.getJSONArray("records");

                    if (records != null && !records.isEmpty()) {
                        // 遍历records数组中的会话记录
                        for (Object recordItem : records) {
                            JSONObject record = (JSONObject) recordItem;

                            // 获取会话ID
                            String conversationId = record.getString("conversationId");

                            // 获取会话类型（private：单聊，group：群聊）
                            String conversationType = record.getString("type");

                            // 处理单聊会话（私聊）
                            if ("private".equals(conversationType)) {
                                // 获取用户信息
                                JSONObject userInfo = record.getJSONObject("userInfo");

                                // 获取最后一条消息信息
                                JSONObject lastMessage = record.getJSONObject("lastMessage");

                                // 获取未读消息数
                                Integer unreadCount = record.getInteger("unread");
                                if (unreadCount == null) {
                                    unreadCount = 0;
                                }

                                if (userInfo != null) {
                                    Map<String, Object> patientInfo = new HashMap<>();
                                    patientInfo.put("id", conversationId);
                                    patientInfo.put("name", userInfo.getString("nickname"));
                                    patientInfo.put("phoneNumber", conversationId); // conversationId通常就是手机号
                                    patientInfo.put("avatar", userInfo.getString("avatarUrl"));
                                    patientInfo.put("imUserId", conversationId);
                                    patientInfo.put("chatGroupId", ""); // 单聊没有群聊ID

                                    // 处理最后一条消息
                                    if (lastMessage != null) {
                                        String lastMsgContent = "";
                                        JSONObject body = lastMessage.getJSONObject("body");
                                        if (body != null) {
                                            String msgType = lastMessage.getString("type");
                                            if ("text".equals(msgType)) {
                                                lastMsgContent = body.getString("text");
                                            } else if ("image".equals(msgType)) {
                                                lastMsgContent = "[图片]";
                                            } else if ("audio".equals(msgType)) {
                                                lastMsgContent = "[语音]";
                                            } else if ("video".equals(msgType)) {
                                                lastMsgContent = "[视频]";
                                            } else if ("group_sys_notice".equals(msgType) || "sys_notice".equals(msgType)) {
                                                // 处理系统通知消息
                                                String tips = body.getString("tips");
                                                if (StringUtils.isNotEmpty(tips)) {
                                                    lastMsgContent = tips;
                                                } else {
                                                    lastMsgContent = "系统通知";
                                                }
                                            } else {
                                                lastMsgContent = "[其他消息]";
                                            }
                                        }
                                        patientInfo.put("lastMessage", lastMsgContent);
                                        patientInfo.put("lastMessageTime", lastMessage.getLong("time"));
                                    } else {
                                        patientInfo.put("lastMessage", "");
                                        patientInfo.put("lastMessageTime", null);
                                    }

                                    patientInfo.put("unreadCount", unreadCount);

                                    patientList.add(patientInfo);
                                }
                            }
                            // 处理群聊会话
                            else if ("group".equals(conversationType)) {
                                // 获取群组信息
                                JSONObject groupInfo = record.getJSONObject("groupInfo");

                                // 获取最后一条消息信息
                                JSONObject lastMessage = record.getJSONObject("lastMessage");

                                // 获取未读消息数
                                Integer unreadCount = record.getInteger("unread");
                                if (unreadCount == null) {
                                    unreadCount = 0;
                                }

                                if (groupInfo != null) {
                                    Map<String, Object> groupChatInfo = new HashMap<>();
                                    groupChatInfo.put("id", conversationId);
                                    groupChatInfo.put("name", groupInfo.getString("name"));
                                    groupChatInfo.put("phoneNumber", ""); // 群聊没有手机号
                                    groupChatInfo.put("avatar", groupInfo.getString("avatarUrl"));
                                    groupChatInfo.put("imUserId", conversationId);
                                    groupChatInfo.put("chatGroupId", conversationId); // 群聊ID
                                    groupChatInfo.put("isGroup", true); // 标识这是群聊

                                    // 处理最后一条消息
                                    if (lastMessage != null) {
                                        String lastMsgContent = "";
                                        JSONObject body = lastMessage.getJSONObject("body");
                                        if (body != null) {
                                            String msgType = lastMessage.getString("type");
                                            if ("text".equals(msgType)) {
                                                lastMsgContent = body.getString("text");
                                            } else if ("image".equals(msgType)) {
                                                lastMsgContent = "[图片]";
                                            } else if ("audio".equals(msgType)) {
                                                lastMsgContent = "[语音]";
                                            } else if ("video".equals(msgType)) {
                                                lastMsgContent = "[视频]";
                                            } else if ("group_sys_notice".equals(msgType)) {
                                                // 处理群聊系统通知消息
                                                String tips = body.getString("tips");
                                                if (StringUtils.isNotEmpty(tips)) {
                                                    lastMsgContent = tips;
                                                } else {
                                                    lastMsgContent = "系统通知";
                                                }
                                            } else {
                                                lastMsgContent = "[其他消息]";
                                            }
                                        }
                                        groupChatInfo.put("lastMessage", lastMsgContent);
                                        groupChatInfo.put("lastMessageTime", lastMessage.getLong("time"));
                                    } else {
                                        groupChatInfo.put("lastMessage", "");
                                        groupChatInfo.put("lastMessageTime", null);
                                    }

                                    groupChatInfo.put("unreadCount", unreadCount);

                                    patientList.add(groupChatInfo);
                                }
                            }
                        }
                    }
                }
                return success(patientList);
            } else {
                return success(new ArrayList<>());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return error("获取患者列表失败: " + e.getMessage());
        }
    }

    // @PreAuthorize("@ss.hasPermi('exchange:imUser:query')")
    @GetMapping(value = "/getImUserBySysUserId/{userId}")
    public AjaxResult getImUserBySysUserId(@PathVariable("userId") String userId) {
        LambdaQueryWrapper<FatImUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatImUser::getSysUserId, userId);
        FatImUser one = fatImUserService.getOne(wrapper);
        return success(one);
    }

    /**
     * 新增IM通讯录
     */
    @PreAuthorize("@ss.hasPermi('exchange:imUser:add')")
    @Log(title = "IM通讯录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FatImUser fatImUser) {
        return toAjax(fatImUserService.save(fatImUser));
    }

    /**
     * 修改IM通讯录
     */
    @PreAuthorize("@ss.hasPermi('exchange:imUser:edit')")
    @Log(title = "IM通讯录", businessType = BusinessType.UPDATE)
    @PutMapping
    @Transactional
    public AjaxResult edit(@RequestBody FatImUser fatImUser) {
        imService.update_user_info_ex(fatImUser.getUserId(), fatImUser.getNickname(), fatImUser.getFaceUrl(), "");
        String sysUserId = fatImUser.getSysUserId();
        String faceUrl = fatImUser.getFaceUrl();
        if (StringUtils.isNotEmpty(sysUserId) && StringUtils.isNotEmpty(faceUrl)) {
            SysUser sysUser = sysUserService.selectUserById(Long.valueOf(sysUserId));
            // sysUser.setAvatar(faceUrl);
            // sysUserService.updateUser(sysUser);
            sysUserService.updateUserAvatar(sysUser.getUserName(), faceUrl);
        }
        return toAjax(fatImUserService.updateFatImUser(fatImUser));
    }

    /**
     * 删除IM通讯录
     */
    @PreAuthorize("@ss.hasPermi('exchange:imUser:remove')")
    @Log(title = "IM通讯录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable String[] userIds) {
        return toAjax(fatImUserService.deleteFatImUserByUserIds(userIds));
    }

    /**
     * 查询机构im user和groups
     *
     * @return
     */
    @GetMapping("/unitIMs/{unitId}")
    public AjaxResult unitIms(@PathVariable("unitId") String unitId) {
        LambdaQueryWrapper<FatImUnit> imUserLambdaQueryWrapper = new LambdaQueryWrapper<>();
        imUserLambdaQueryWrapper.eq(FatImUnit::getUnitId, unitId);
        List<FatImUnit> list = fatImUnitService.list(imUserLambdaQueryWrapper);
        Set<String> users = list.stream().map(item -> item.getImUserId()).collect(Collectors.toSet());

        LambdaQueryWrapper<FatPatientDoctor> doctorLambdaQueryWrapper = new LambdaQueryWrapper<>();
        doctorLambdaQueryWrapper.eq(FatPatientDoctor::getUnitId, unitId);
        List<FatPatientDoctor> doctorList = fatPatientDoctorService.list(doctorLambdaQueryWrapper);
        Set<String> groups = doctorList.stream().map(item -> item.getChatGroupId()).collect(Collectors.toSet());

        Map<String, Object> map = new HashMap<>();
        map.put("users", users);
        map.put("groups", groups);
        return AjaxResult.success(map);
    }

    /**
     * 获取本机构人员列表（用于群组创建）
     *
     * @apiNote 查询条件：本机构 + 创建人为yeim
     * @return AjaxResult 返回结果
     * @apiSuccess {Number} code 状态码，200表示成功
     * @apiSuccess {String} msg 返回消息
     * @apiSuccess {Object[]} data 人员列表数据
     * @apiSuccess {String} data.userId IM用户ID
     * @apiSuccess {String} data.nickname 用户昵称
     * @apiSuccess {String} data.faceUrl 用户头像URL
     * @apiSuccess {String} data.sysUserId 系统用户ID
     * @apiSuccess {String} data.phoneNumber 手机号
     * @apiSuccess {String} data.ex 扩展字段
     *
     * @apiError {Number} code 错误状态码
     * @apiError {String} msg 错误消息
     *
     * @apiExample {curl} 请求示例:
     * curl -X GET "http://localhost:8088/exchange/imUser/unitMembers" \
     *      -H "Authorization: Bearer your-token"
     *
     * @apiExample {json} 成功响应示例:
     * {
     *   "code": 200,
     *   "msg": "操作成功",
     *   "data": [
     *     {
     *       "userId": "user123",
     *       "nickname": "张医生",
     *       "faceUrl": "http://example.com/avatar.jpg",
     *       "sysUserId": "sys123",
     *       "phoneNumber": "13800138000",
     *       "ex": "yehos"
     *     }
     *   ]
     * }
     */
    @GetMapping("/unitMembers")
    public AjaxResult getUnitMembers() {
        try {
            // 获取当前登录用户信息
            String currentUserId = getUserId().toString();

            // 查询当前用户的IM用户信息，获取机构ID
            LambdaQueryWrapper<FatImUser> currentUserWrapper = new LambdaQueryWrapper<>();
            currentUserWrapper.eq(FatImUser::getSysUserId, currentUserId);
            currentUserWrapper.eq(BaseEntity::getCreateBy, "yeim");
            FatImUser currentImUser = fatImUserService.getOne(currentUserWrapper);

            if (currentImUser == null) {
                return error("当前用户未注册IM账号或不是yeim创建的用户");
            }

            String unitId = currentImUser.getUnitId();
            if (StringUtils.isEmpty(unitId)) {
                return error("当前用户未关联机构");
            }

            // 查询本机构下创建人为yeim的所有成员
            LambdaQueryWrapper<FatImUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatImUser::getUnitId, unitId);
//            wrapper.eq(BaseEntity::getCreateBy, "yeim");
            wrapper.isNotNull(FatImUser::getUserId);
            wrapper.ne(FatImUser::getUserId, ""); // 排除空的userId

            List<FatImUser> memberList = fatImUserService.list(wrapper);

            // 转换为前端需要的格式
            List<Map<String, Object>> result = new ArrayList<>();
            for (FatImUser member : memberList) {
                Map<String, Object> memberInfo = new HashMap<>();
                memberInfo.put("userId", member.getUserId());
                memberInfo.put("nickname", member.getNickname());
                memberInfo.put("faceUrl", member.getFaceUrl());
                memberInfo.put("sysUserId", member.getSysUserId());
                memberInfo.put("phoneNumber", member.getPhoneNumber());
                memberInfo.put("ex", member.getEx());
                result.add(memberInfo);
            }

            return success(result);
        } catch (Exception e) {
            e.printStackTrace();
            return error("获取机构人员列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建群组接口（用于在线咨询页面）
     *
     * @apiNote 根据勾选的成员列表创建群组，当前用户自动成为群主
     * @param request 请求参数
     * @return AjaxResult 返回结果
     *
     * @apiParam {String[]} selectedUserIds 必填，选中的成员IM用户ID列表
     * @apiParam {String} groupName 可选，群组名称，默认为"在线咨询群组"
     *
     * @apiSuccess {Number} code 状态码，200表示成功
     * @apiSuccess {String} msg 返回消息
     * @apiSuccess {Object} data 群组创建结果
     * @apiSuccess {String} data.groupId 群组ID
     * @apiSuccess {String} data.groupName 群组名称
     * @apiSuccess {Number} data.memberCount 群组成员数量
     * @apiSuccess {String} data.ownerUserId 群主用户ID
     *
     * @apiError {Number} code 错误状态码
     * @apiError {String} msg 错误消息
     *
     * @apiExample {curl} 请求示例:
     * curl -X POST "http://localhost:8088/exchange/imUser/createChatGroup" \
     *      -H "Content-Type: application/json" \
     *      -H "Authorization: Bearer your-token" \
     *      -d '{
     *        "selectedUserIds": ["user123", "user456"],
     *        "groupName": "医患交流群"
     *      }'
     *
     * @apiExample {json} 成功响应示例:
     * {
     *   "code": 200,
     *   "msg": "操作成功",
     *   "data": {
     *     "groupId": "group789",
     *     "groupName": "医患交流群",
     *     "memberCount": 3,
     *     "ownerUserId": "currentUser"
     *   }
     * }
     *
     * @apiExample {json} 错误响应示例:
     * {
     *   "code": 500,
     *   "msg": "请选择群组成员"
     * }
     */
    @PostMapping("/createChatGroup")
    public AjaxResult createChatGroup(@RequestBody Map<String, Object> request) {
        try {
            // 获取请求参数
            @SuppressWarnings("unchecked")
            List<String> selectedUserIds = (List<String>) request.get("selectedUserIds");
            String groupName = (String) request.get("groupName");

            if (selectedUserIds == null || selectedUserIds.isEmpty()) {
                return error("请选择群组成员");
            }

            if (StringUtils.isEmpty(groupName)) {
                groupName = "在线咨询群组";
            }

            // 获取当前登录用户信息
            String currentUserId = getUserId().toString();

            // 查询当前用户的IM用户信息
            LambdaQueryWrapper<FatImUser> currentUserWrapper = new LambdaQueryWrapper<>();
            currentUserWrapper.eq(FatImUser::getSysUserId, currentUserId);
            currentUserWrapper.eq(BaseEntity::getCreateBy, "yeim");
            FatImUser currentImUser = fatImUserService.getOne(currentUserWrapper);

            if (currentImUser == null) {
                return error("当前用户未注册IM账号");
            }

            String unitId = currentImUser.getUnitId();
            if (StringUtils.isEmpty(unitId)) {
                return error("当前用户未关联机构");
            }

            // 验证所选成员都属于同一机构且创建人为yeim
            LambdaQueryWrapper<FatImUser> memberWrapper = new LambdaQueryWrapper<>();
            memberWrapper.in(FatImUser::getUserId, selectedUserIds);
            memberWrapper.eq(FatImUser::getUnitId, unitId);

            List<FatImUser> selectedMembers = fatImUserService.list(memberWrapper);

            if (selectedMembers.size() != selectedUserIds.size()) {
                return error("部分选择的成员不属于当前机构或不是有效用户");
            }

            // 准备群组成员列表
            List<String> memberUserIDs = new ArrayList<>();
            memberUserIDs.add(currentImUser.getUserId()); // 添加当前用户作为群主

            // 添加选中的成员
            for (FatImUser member : selectedMembers) {
                if (!member.getUserId().equals(currentImUser.getUserId())) {
                    memberUserIDs.add(member.getUserId());
                }
            }

            // 管理员列表（当前用户作为管理员）
            List<String> adminUserIDs = new ArrayList<>();
            adminUserIDs.add(currentImUser.getUserId());

            // 生成群组ID
            String generatedGroupId = UUID.randomUUID().toString().replace("-", "");

            // 调用YeImService创建群组
            String createResult = yeImService.createGroup(
                memberUserIDs,
                adminUserIDs,
                currentImUser.getUserId(),
                generatedGroupId, // 使用生成的UUID作为groupID
                groupName,
                0, // needVerification: 0-自由加入
                currentImUser.getUserId()
            );

            // 检查创建结果，如果包含success则认为创建成功
            if (StringUtils.isNotEmpty(createResult) && createResult.contains("success")) {
                // 群组创建成功后，插入数据到fat_im_unit表
                try {
                    FatImUnit fatImUnit = new FatImUnit();
                    fatImUnit.setId(UUID.randomUUID().toString().replace("-", "")); // 生成主键ID
                    fatImUnit.setUnitId(unitId); // 机构ID
                    fatImUnit.setImUserId(currentUserId); // 群组ID作为IM用户ID
                    fatImUnit.setSysUserId(currentUserId); // 创建者的系统用户ID
                    fatImUnit.setImName(groupName); // 群组名称作为IM名称
                    fatImUnit.setCreateBy(getUsername()); // 创建者
                    fatImUnit.setCreateTime(new Date()); // 创建时间

                    // 插入到数据库
                    fatImUnitService.save(fatImUnit);

                    System.out.println("群组信息已插入fat_im_unit表: groupId=" + generatedGroupId + ", unitId=" + unitId);
                } catch (Exception e) {
                    System.err.println("插入fat_im_unit表失败: " + e.getMessage());
                    e.printStackTrace();
                    // 这里不影响群组创建的成功返回，只是记录日志
                }

                // 返回创建成功的群组信息
                Map<String, Object> result = new HashMap<>();
                result.put("groupId", generatedGroupId);
                result.put("groupName", groupName);
                result.put("memberCount", memberUserIDs.size());
                result.put("ownerUserId", currentImUser.getUserId());

                return success(result);
            } else {
                return error("群组创建失败");
            }

        } catch (Exception e) {
            e.printStackTrace();
            return error("创建群组失败: " + e.getMessage());
        }
    }

    /**
     * 获取群成员列表
     * @param groupId 群组ID
     * @return 群成员列表
     */
    @GetMapping("/groupMembers")
    public AjaxResult getGroupMembers(@RequestParam("groupId") String groupId) {
        try {
            if (StringUtils.isEmpty(groupId)) {
                return error("群组ID不能为空");
            }

            // 获取当前登录用户信息
            String currentUserId = getUserId().toString();

            // 查询当前用户的IM用户信息
            LambdaQueryWrapper<FatImUser> currentUserWrapper = new LambdaQueryWrapper<>();
            currentUserWrapper.eq(FatImUser::getSysUserId, currentUserId);
            currentUserWrapper.eq(BaseEntity::getCreateBy, "yeim");
            FatImUser currentImUser = fatImUserService.getOne(currentUserWrapper);

            if (currentImUser == null) {
                return error("当前用户未注册IM账号");
            }

            // 调用YeImService获取群成员列表
            JSONArray groupMembers = yeImService.getGroupMembers(groupId, currentImUser.getUserId());

            if (groupMembers != null && !groupMembers.isEmpty()) {
                // 转换为前端需要的格式
                List<Map<String, Object>> memberList = new ArrayList<>();

                for (int i = 0; i < groupMembers.size(); i++) {
                    JSONObject member = groupMembers.getJSONObject(i);

                    Map<String, Object> memberInfo = new HashMap<>();
                    memberInfo.put("userId", member.getString("userId"));

                    // 处理用户信息
                    JSONObject userInfo = member.getJSONObject("userInfo");
                    if (userInfo != null) {
                        memberInfo.put("nickname", userInfo.getString("nickname"));
                        memberInfo.put("avatarUrl", userInfo.getString("avatarUrl"));
                    } else {
                        memberInfo.put("nickname", "未知用户");
                        memberInfo.put("avatarUrl", null);
                    }

                    // 处理角色信息 (isAdmin: 0-普通成员, 1-管理员)
                    Integer isAdmin = member.getInteger("isAdmin");
                    memberInfo.put("role", (isAdmin != null && isAdmin == 1) ? "admin" : "member");

                    // 处理时间信息
                    Long joinAt = member.getLong("joinAt");
                    memberInfo.put("joinTime", joinAt != null ? joinAt : System.currentTimeMillis());

                    // 尝试从本地数据库获取手机号
                    String userId = member.getString("userId");
                    if (StringUtils.isNotEmpty(userId)) {
                        LambdaQueryWrapper<FatImUser> userWrapper = new LambdaQueryWrapper<>();
                        userWrapper.eq(FatImUser::getUserId, userId);
                        FatImUser imUser = fatImUserService.getOne(userWrapper);
                        if (imUser != null) {
                            memberInfo.put("phoneNumber", imUser.getPhoneNumber());
                        } else {
                            memberInfo.put("phoneNumber", null);
                        }
                    } else {
                        memberInfo.put("phoneNumber", null);
                    }

                    memberList.add(memberInfo);
                }

                return success(memberList);
            } else {
                return success(new ArrayList<>());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return error("获取群成员列表失败: " + e.getMessage());
        }
    }

    /**
     * 语音助手接口 - 支持语音转文字和直接跳转到聊天界面
     */
    @PostMapping("/voiceAssistant")
    public AjaxResult voiceAssistant(@RequestBody AIMessage aiMessage) {

        try {
            // 如果前端没有传递URL，则使用测试URL（用于调试）
            String recognizedText = "";
            // 处理语音或文本输入
            Map<String, Object> textElem = (Map<String, Object>) aiMessage.getTextElem();
            if (textElem != null) {
                // 文本输入
                recognizedText = textElem.get("content").toString();
            } else if (aiMessage.getUrl() != null) {
                // 语音输入，先转换为文字
                recognizedText = fatImUserService.changeVoiceToText(aiMessage.getUrl());
            } else {
                return AjaxResult.error("输入内容为空");
            }

            // 检查是否为空
            if (recognizedText == null || recognizedText.trim().isEmpty()) {
                VoiceAssistantResponse errorResponse = new VoiceAssistantResponse();
                errorResponse.setAction("default");
                errorResponse.setMessage("未识别到有效内容，请重新尝试");
                errorResponse.setSuccess(false);
                return AjaxResult.success(errorResponse);
            }

            // 获取当前登录用户信息
            String currentUserId = getUserId().toString();

            // 查询当前用户的IM用户信息
            LambdaQueryWrapper<FatImUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(FatImUser::getSysUserId, currentUserId);
            FatImUser currentImUser = fatImUserService.getOne(wrapper);

            if (currentImUser != null && currentImUser.getUnitId() != null) {
                // 查询机构的IM用户
                LambdaQueryWrapper<FatImUser> unitWrapper = new LambdaQueryWrapper<>();
                unitWrapper.eq(FatImUser::getUnitId, currentImUser.getUnitId())
                        .eq(FatImUser::getEx, "yehos");
                FatImUser unitImUser = fatImUserService.getOne(unitWrapper);

                if (unitImUser != null) {
                    try {
                        // 构建响应对象，使用自定义参数，便于前端处理
                        VoiceAssistantResponse response = new VoiceAssistantResponse();
                        response.setAction("chat");
                        // 不再使用URL跳转，而是提供必要的参数让前端处理
                        Map<String, Object> chatParams = new HashMap<>();
                        chatParams.put("conversationId", unitImUser.getUserId());
                        chatParams.put("nickname", unitImUser.getNickname());
                        chatParams.put("conversationType", "private"); // 使用private而不是数字1
                        chatParams.put("voiceText", recognizedText);
                        response.setParams(chatParams);
                        response.setMessage("正在为您连接到减重AI助手，请稍候...");
                        response.setRecognizedText(recognizedText);
                        response.setSuccess(true);

                        System.out.println("语音助手响应: " + response);
                        return AjaxResult.success(response);
                    } catch (Exception e) {
                        System.err.println("构建跳转URL失败: " + e.getMessage());
                        e.printStackTrace();
                    }
                }
            }

            // 如果没有找到机构用户，返回默认响应
            VoiceAssistantResponse defaultResponse = new VoiceAssistantResponse();
            defaultResponse.setAction("default");
            defaultResponse.setMessage("已识别您的语音: " + recognizedText);
            defaultResponse.setRecognizedText(recognizedText);
            defaultResponse.setSuccess(true);
            return AjaxResult.success(defaultResponse);

        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("语音助手处理失败: " + e.getMessage());

            VoiceAssistantResponse errorResponse = new VoiceAssistantResponse();
            errorResponse.setAction("default");
            errorResponse.setMessage("处理失败，请重试");
            errorResponse.setSuccess(false);
            return AjaxResult.success(errorResponse);
        }
    }

    /**
     * 更新即时通讯用户信息
     * @param fatImUser
     * @return
     */
    @PostMapping("/updateYeimUserInfo")
    public AjaxResult updateYeimUserInfo(@RequestBody FatImUser fatImUser){
        String sysUserId = fatImUser.getSysUserId();
        if(sysUserId == null){
            return AjaxResult.error("系统用户ID不能为空");
        }
        LambdaQueryWrapper<FatImUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FatImUser::getSysUserId, sysUserId);
        FatImUser imUser = fatImUserService.getOne(wrapper);
        if(imUser == null){
            return AjaxResult.error("未找到对应的IM用户信息");
        }
        imUser.setFaceUrl(fatImUser.getFaceUrl());
        imUser.setUpdateTime(new Date());
        fatImUserService.updateById(imUser);

        SysUser user = sysUserService.selectUserById(Long.valueOf(sysUserId));
        user.setAvatar(fatImUser.getFaceUrl());
        sysUserService.updateUserProfile(user);

        yeImService.update_user_info_ex(imUser.getUserId(),
                imUser.getNickname(),
                imUser.getFaceUrl(),null);
        return AjaxResult.success("更新成功");
    }

    /**
     * 生成群聊系统通知的用户友好文本
     * @param message 系统通知消息
     * @return 用户友好的通知文本
     */
    private String generateGroupNoticeText(JSONObject message) {
        try {
            String from = message.getString("from");
            JSONObject body = message.getJSONObject("body");

            // 根据消息的具体内容生成通知文本
            // 这里可以根据实际的群聊系统通知类型进行扩展

            // 如果body中有特定的通知信息，可以解析并生成相应的文本
            if (body != null) {
                // 检查是否有tips字段（通常包含系统通知的描述）
                String tips = body.getString("tips");
                if (StringUtils.isNotEmpty(tips)) {
                    return tips;
                }

                // 检查是否有text字段
                String text = body.getString("text");
                if (StringUtils.isNotEmpty(text)) {
                    return text;
                }

                // 检查是否有content字段
                String content = body.getString("content");
                if (StringUtils.isNotEmpty(content)) {
                    return content;
                }
            }

            // 如果没有具体的通知内容，生成默认的系统通知文本
            if ("0".equals(from)) {
                // from为"0"通常表示系统消息
                return "系统通知";
            } else {
                // 其他情况，可能是用户操作相关的通知
                return "群组消息通知";
            }

        } catch (Exception e) {
            e.printStackTrace();
            return "系统通知";
        }
    }

}

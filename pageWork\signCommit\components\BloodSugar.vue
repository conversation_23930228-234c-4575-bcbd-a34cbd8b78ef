<template>
	<view class="sugar">
		<view class="sugar-current">
			<view class="weight-title">
				血糖记录
				<view class="weight-link" @click="showMeasurementGuide">
					<text>(如何正确测量血糖)</text>
				</view>
			</view>
			
			<view style="display: flex; flex-direction: row; gap: 10px;">
				<u-tag :bg-color="timeSlot === '早上' ? '#4f41ac' : '#606163'"
					:border-color="timeSlot === '早上' ? '#4f41ac' : '#606163'" text="早上" @click="selectTime('早上')" />
				<u-tag :bg-color="timeSlot === '中午' ? '#4f41ac' : '#606163'"
					:border-color="timeSlot === '中午' ? '#4f41ac' : '#606163'" text="中午" @click="selectTime('中午')" />
				<u-tag :bg-color="timeSlot === '晚上' ? '#4f41ac' : '#606163'"
					:border-color="timeSlot === '晚上' ? '#4f41ac' : '#606163'" text="晚上" @click="selectTime('晚上')" />
			</view>

			<!-- 早上填写数据 -->
			<view class="sugar-time-block" v-if="timeSlot === '早上'">
				<view class="sugar-input">
					<input style="height: 60rpx;" type="digit" v-model="before" />
					<text>mmol</text>
				</view>
				<view class="sugar-warn">
					<text>餐前血糖</text>
				</view>
				<view class="sugar-input">
					<input style="height: 60rpx;" type="digit" v-model="after" />
					<text>mmol</text>
				</view>
				<view class="sugar-warn">
					<text>餐后血糖</text>
				</view>
			</view>

			<!-- 中午填写数据 -->
			<view class="sugar-time-block" v-if="timeSlot === '中午'">
				<view class="sugar-input">
					<input style="height: 60rpx;" type="digit" v-model="before" />
					<text>mmol</text>
				</view>
				<view class="sugar-warn">
					<text>餐前血糖</text>
				</view>
				<view class="sugar-input">
					<input style="height: 60rpx;" type="digit" v-model="after" />
					<text>mmol</text>
				</view>
				<view class="sugar-warn">
					<text>餐后血糖</text>
				</view>
			</view>

			<!-- 晚上填写数据 -->
			<view class="sugar-time-block" v-if="timeSlot === '晚上'">
				<view class="sugar-input">
					<input style="height: 60rpx;" type="digit" v-model="before" />
					<text>mmol</text>
				</view>
				<view class="sugar-warn">
					<text>餐前血糖</text>
				</view>
				<view class="sugar-input">
					<input style="height: 60rpx;" type="digit" v-model="after" />
					<text>mmol</text>
				</view>
				<view class="sugar-warn">
					<text>餐后血糖</text>
				</view>
			</view>
		</view>
		<!-- 显示填写记录 -->
		<view class="record-card" v-for="(record, index) in bloodList" :key="index">
			<view class="record-time">
				{{ record.timeSlot }}
			</view>
			<view class="sugar-info" style="color: gray; display: flex;">
					<view style="width: 50%;">餐前血糖：{{ record.before? record.before + 'mmol' : '' }}</view>
					<view style="width: 50%;">餐后血糖：{{ record.after? record.after + 'mmol' : '' }}</view>
			</view>
			

			
		</view>

		<!-- 血糖变化趋势图表 -->
		<view class="blood-sugar-trend">
			<view class="weight-title">
				变化趋势
			</view>
			<view class="weight-unit">
				单位：mmol/L
			</view>

			<!-- 时间段过滤选项 -->
			<view class="chart-time-filter">
				<view :class="['filter-tag', chartTimeFilter === 'all' && 'filter-tag-active']"
					@click="changeChartTimeFilter('all')">
					全部
				</view>
				<view :class="['filter-tag', chartTimeFilter === '早上' && 'filter-tag-active']"
					@click="changeChartTimeFilter('早上')">
					早上
				</view>
				<view :class="['filter-tag', chartTimeFilter === '中午' && 'filter-tag-active']"
					@click="changeChartTimeFilter('中午')">
					中午
				</view>
				<view :class="['filter-tag', chartTimeFilter === '晚上' && 'filter-tag-active']"
					@click="changeChartTimeFilter('晚上')">
					晚上
				</view>
			</view>

			<view class="weight-charts" v-if="chartData">
				<qiun-data-charts type="line" :canvas2d="true" :activeType="hollow"
					:chartData="chartData" />
			</view>
			<u-empty v-else></u-empty>
			<view class="weight-during">
				<view :class="['weight-during-one',item.id===during&&'weight-during-active']" v-for="item in duringList"
					:key="item.id" @click="changeDuring(item.id)">
					{{item.name?item.name:''}}
				</view>
			</view>
		</view>

		<!-- 提交按钮 -->
		<view class="footer">
			<view class="footer-submit" v-if="timeSlot === '早上'">
				<view class="footer-submit-button" @click="submitData">提交</view>
			</view>
			<view class="footer-submit" v-if="timeSlot === '中午'">
				<view class="footer-submit-button" @click="submitData2">提交</view>
			</view>
			<view class="footer-submit" v-if="timeSlot === '晚上'">
				<view class="footer-submit-button" @click="submitData3">提交</view>
			</view>
		</view>
	</view>
</template>

<script>
	import * as SignApi from "@/api/signCommit.js";
	import * as UserApi from "@/api/user.js";
	import dayjs from 'dayjs';
	import {
		subscribe
	} from "@/utils/common.js";
	export default {
		props: {
			signTime: {
				type: String,
				default: '',
			}
		},
		data() {
			return {
				before: '',
				after: '',
				timeSlot: '早上',
				bloodList: [],
				num: '',
				newWeightSign: null,
				patientCenterInfo: null,
				// 图表相关数据
				chartData: null,
				originalChartData: null, // 保存原始图表数据
				chartTimeFilter: 'all', // 图表时间段过滤器
				duringList: [{
					name: '日',
					id: 'day'
				}, {
					name: '周',
					id: 'week'
				}, {
					name: '月',
					id: 'month'
				}],
				during: 'day'
			}
		},
		onReady() {
			UserApi.patientCenterInfo().then(res => {
				console.log("获取centerInfo", res);
				if (res.code == 200) {
					this.patientCenterInfo = res.data;
				}
			});
			this.fetchAllBloodRecords();
			// 获取图表数据
			this.getChartData();
		},
		watch: {
			signTime: {
				handler(val) {
					console.log("watch", this.signTime);
					this.fetchAllBloodRecords();
					this.getChartData();
				},
				immediate: true
			}
		},
		methods: {
			selectTime(time) {
				this.timeSlot = time;
			},
			fetchAllBloodRecords() {
				const timeSlots = ['早上', '中午', '晚上'];
				const requests = timeSlots.map(slot => {
					const query = {
						signType: '血糖',
						signTime: this.signTime,
						timeSlot: slot
					};
					return SignApi.getSignDataByTypeAndTime(query);
				});

				Promise.all(requests).then(results => {
					this.bloodList = results.reduce((acc, res) => {
						if (res.code === 200 && res.rows) {
							return acc.concat(res.rows);
						}
						return acc;
					}, []);
				});
			},
			showMeasurementGuide() {
				console.log("预览");
				uni.previewImage({
					urls: [
						"https://fat-1323860534.cos.ap-nanjing.myqcloud.com/36a6ccb8-54f1-4288-9511-98dbdeacef78.jpg",
					],
					current: "https://fat-1323860534.cos.ap-nanjing.myqcloud.com/36a6ccb8-54f1-4288-9511-98dbdeacef78.jpg",
				});
			},
			changeDuring(during) {
				this.during = during;
				this.getChartData();
			},
			changeChartTimeFilter(timeFilter) {
				this.chartTimeFilter = timeFilter;
				this.filterChartData();
			},
			filterChartData() {
				if (!this.originalChartData) {
					return;
				}

				if (this.chartTimeFilter === 'all') {
					this.chartData = this.originalChartData;
					return;
				}

				// 过滤数据，只显示指定时间段的数据
				const filteredCategories = [];
				const filteredBeforeValues = [];
				const filteredAfterValues = [];

				this.originalChartData.categories.forEach((category, index) => {
					// 检查category是否包含指定的时间段，例如"7月28号[早上]"
					if (category.includes(`[${this.chartTimeFilter}]`)) {
						filteredCategories.push(category);
						filteredBeforeValues.push(this.originalChartData.series[0].data[index]);
						filteredAfterValues.push(this.originalChartData.series[1].data[index]);
					}
				});

				if (filteredCategories.length > 0) {
					this.chartData = {
						"categories": filteredCategories,
						"series": [{
							"name": "餐前血糖",
							"data": filteredBeforeValues,
							"color": "#4ECDC4",
							show: true,
							fontSize: 14
						}, {
							"name": "餐后血糖",
							"data": filteredAfterValues,
							"color": "#FF6B6B",
							show: true,
							fontSize: 14
						}]
					};
				} else {
					this.chartData = null;
				}
			},
			getChartData() {
				let query = {
					signTime: this.signTime + " 00:00:01",
					during: this.during,
					patientId: this.$store.state.unit.unionId,
				}
				SignApi.getBloodSugarChartData(query).then(res => {
					console.log("血糖图表数据", res)

					if (res.code == 200) {
						if (res.data && res.data.hasData) {
							let data = res.data;
							let months = data.months;
							let beforeValues = data.beforeValues || []; // 餐前血糖数据
							let afterValues = data.afterValues || []; // 餐后血糖数据

							console.log('血糖图表数据', months, beforeValues, afterValues)
							if (months && months.length > 0) {
								// 保存原始数据
								this.originalChartData = {
									"categories": months,
									"series": [{
										"name": "餐前血糖",
										"data": beforeValues,
										"color": "#4ECDC4",
										show: true,
										fontSize: 14
									}, {
										"name": "餐后血糖",
										"data": afterValues,
										"color": "#FF6B6B",
										show: true,
										fontSize: 14
									}]
								};
								// 应用过滤
								this.filterChartData();
							} else {
								this.originalChartData = null;
								this.chartData = null;
							}
						} else {
							this.chartData = null;
						}
					} else {
						this.chartData = null;
					}
				})
			},
			submitData() {
				console.log(this.before, this.after);
				if (!this.before || !this.after) {
					uni.showToast({
						title: "血糖值不能为空",
						icon: "none"
					});
					return;
				}
				const now = dayjs();
				const commit = dayjs(this.signTime);
				if (now.isBefore(commit)) {
					uni.$u.toast("不支持提交将来时间的数据");
					return;
				}
				let data = {
					signTime: this.signTime,
					before: this.before,
					after: this.after
				};
				let commitData = {
					formValue: JSON.stringify(data),
					signId: '1821520437748940801',
					formText: `{"config":{"version":"2.2","formRef":"elForm","formModel":"form","rules":"rules","size":"medium","labelPosition":"right","labelWidth":80,"formRules":"rules","gutter":15,"disabled":false,"dynamicTableAllowed":true},"list":[{"id":"signTime","_id":"signTime","compType":"date","ele":"el-date-picker","compName":"日期","compIcon":"date","viewType":"text","config":true,"showLabel":true,"label":"日期","labelWidth":80,"placeholder":"请选择","required":false,"maxLength":50,"gutter":15,"span":24,"clearable":true,"disabled":false,"readonly":false,"value":"","rules":[],"type":"date","format":"yyyy-MM-dd","value-format":"yyyy-MM-dd","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","classStyle":[],"cssStyle":"","layout":"colItem"},{"id":"before","_id":"before","compType":"input","ele":"el-input","compName":"单行文本","compIcon":"input","viewType":"text","config":true,"showLabel":true,"label":"餐前","labelWidth":80,"placeholder":"请输入餐前血糖","required":false,"maxLength":50,"gutter":15,"span":24,"width":"100%","clearable":true,"disabled":false,"readonly":false,"status":"normal","prefix-icon":"","suffix-icon":"","value":"","rules":[{"rule":"^[0-9]*$","msg":"您输入的内容不符合纯数字规则"}],"rulesType":"number","prepend":"","append":"mmol","cssStyle":"","classStyle":[],"layout":"colItem"},{"id":"after","_id":"after","compType":"input","ele":"el-input","compName":"单行文本","compIcon":"input","viewType":"text","config":true,"showLabel":true,"label":"餐后","labelWidth":80,"placeholder":"请输入餐后血糖","required":false,"maxLength":50,"gutter":15,"span":24,"width":"100%","clearable":true,"disabled":false,"readonly":false,"status":"normal","prefix-icon":"","suffix-icon":"","value":"","rules":[{"rule":"^[0-9]*$","msg":"您输入的内容不符合纯数字规则"}],"rulesType":"number","prepend":"","append":"mmol","cssStyle":"","classStyle":[],"layout":"colItem"}]}`,
					idNo: this.patientCenterInfo.idNo,
					signName: '血糖',
					signTime: this.signTime,
					patientName: this.patientCenterInfo.name,
					patientId: this.$store.state.unit.unionId,
					unitId: this.$store.state.unit.id,
					timeSlot: '早上'
				};
				SignApi.signRecordSave(commitData).then(res => {
					console.log(res);
					if (res.code == 200) {
						this.$emit('submit', {
							before: this.before,
							after: this.after,
							timeSlot: this.timeSlot
						});
						this.fetchAllBloodRecords();
						this.before = '';
						this.after = '';
						this.getChartData(); // 提交后刷新图表数据
						subscribe();
						setTimeout(() => {
							uni.showToast({
								title: "提交成功"
							});
						}, 700);
					}
				});
			},
			submitData2() {
				console.log(this.before, this.after);
				if (!this.before || !this.after) {
					uni.showToast({
						title: "血糖值不能为空",
						icon: "none"
					});
					return;
				}
				const now = dayjs();
				const commit = dayjs(this.signTime);
				if (now.isBefore(commit)) {
					uni.$u.toast("不支持提交将来时间的数据");
					return;
				}
				let data = {
					signTime: this.signTime,
					before: this.before,
					after: this.after
				};
				let commitData = {
					formValue: JSON.stringify(data),
					signId: '1821520437748940801',
					formText: `{"config":{"version":"2.2","formRef":"elForm","formModel":"form","rules":"rules","size":"medium","labelPosition":"right","labelWidth":80,"formRules":"rules","gutter":15,"disabled":false,"dynamicTableAllowed":true},"list":[{"id":"signTime","_id":"signTime","compType":"date","ele":"el-date-picker","compName":"日期","compIcon":"date","viewType":"text","config":true,"showLabel":true,"label":"日期","labelWidth":80,"placeholder":"请选择","required":false,"maxLength":50,"gutter":15,"span":24,"clearable":true,"disabled":false,"readonly":false,"value":"","rules":[],"type":"date","format":"yyyy-MM-dd","value-format":"yyyy-MM-dd","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","classStyle":[],"cssStyle":"","layout":"colItem"},{"id":"before","_id":"before","compType":"input","ele":"el-input","compName":"单行文本","compIcon":"input","viewType":"text","config":true,"showLabel":true,"label":"餐前","labelWidth":80,"placeholder":"请输入餐前血糖","required":false,"maxLength":50,"gutter":15,"span":24,"width":"100%","clearable":true,"disabled":false,"readonly":false,"status":"normal","prefix-icon":"","suffix-icon":"","value":"","rules":[{"rule":"^[0-9]*$","msg":"您输入的内容不符合纯数字规则"}],"rulesType":"number","prepend":"","append":"mmol","cssStyle":"","classStyle":[],"layout":"colItem"},{"id":"after","_id":"after","compType":"input","ele":"el-input","compName":"单行文本","compIcon":"input","viewType":"text","config":true,"showLabel":true,"label":"餐后","labelWidth":80,"placeholder":"请输入餐后血糖","required":false,"maxLength":50,"gutter":15,"span":24,"width":"100%","clearable":true,"disabled":false,"readonly":false,"status":"normal","prefix-icon":"","suffix-icon":"","value":"","rules":[{"rule":"^[0-9]*$","msg":"您输入的内容不符合纯数字规则"}],"rulesType":"number","prepend":"","append":"mmol","cssStyle":"","classStyle":[],"layout":"colItem"}]}`,
					idNo: this.patientCenterInfo.idNo,
					signName: '血糖',
					signTime: this.signTime,
					patientName: this.patientCenterInfo.name,
					patientId: this.$store.state.unit.unionId,
					unitId: this.$store.state.unit.id,
					timeSlot: '中午'
				};
				SignApi.signRecordSave(commitData).then(res => {
					console.log(res);
					if (res.code == 200) {
						this.$emit('submit', {
							before: this.before,
							after: this.after,
							timeSlot: this.timeSlot
						});
						this.fetchAllBloodRecords();
						this.before = '';
						this.after = '';
						this.getChartData(); // 提交后刷新图表数据
						subscribe();
						setTimeout(() => {
							uni.showToast({
								title: "提交成功"
							});
						}, 700);
					}
				});
			},
			submitData3() {
				console.log(this.before, this.after);
				if (!this.before || !this.after) {
					uni.showToast({
						title: "血糖值不能为空",
						icon: "none"
					});
					return;
				}
				const now = dayjs();
				const commit = dayjs(this.signTime);
				if (now.isBefore(commit)) {
					uni.$u.toast("不支持提交将来时间的数据");
					return;
				}
				let data = {
					signTime: this.signTime,
					before: this.before,
					after: this.after
				};
				let commitData = {
					formValue: JSON.stringify(data),
					signId: '1821520437748940801',
					formText: `{"config":{"version":"2.2","formRef":"elForm","formModel":"form","rules":"rules","size":"medium","labelPosition":"right","labelWidth":80,"formRules":"rules","gutter":15,"disabled":false,"dynamicTableAllowed":true},"list":[{"id":"signTime","_id":"signTime","compType":"date","ele":"el-date-picker","compName":"日期","compIcon":"date","viewType":"text","config":true,"showLabel":true,"label":"日期","labelWidth":80,"placeholder":"请选择","required":false,"maxLength":50,"gutter":15,"span":24,"clearable":true,"disabled":false,"readonly":false,"value":"","rules":[],"type":"date","format":"yyyy-MM-dd","value-format":"yyyy-MM-dd","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","classStyle":[],"cssStyle":"","layout":"colItem"},{"id":"before","_id":"before","compType":"input","ele":"el-input","compName":"单行文本","compIcon":"input","viewType":"text","config":true,"showLabel":true,"label":"餐前","labelWidth":80,"placeholder":"请输入餐前血糖","required":false,"maxLength":50,"gutter":15,"span":24,"width":"100%","clearable":true,"disabled":false,"readonly":false,"status":"normal","prefix-icon":"","suffix-icon":"","value":"","rules":[{"rule":"^[0-9]*$","msg":"您输入的内容不符合纯数字规则"}],"rulesType":"number","prepend":"","append":"mmol","cssStyle":"","classStyle":[],"layout":"colItem"},{"id":"after","_id":"after","compType":"input","ele":"el-input","compName":"单行文本","compIcon":"input","viewType":"text","config":true,"showLabel":true,"label":"餐后","labelWidth":80,"placeholder":"请输入餐后血糖","required":false,"maxLength":50,"gutter":15,"span":24,"width":"100%","clearable":true,"disabled":false,"readonly":false,"status":"normal","prefix-icon":"","suffix-icon":"","value":"","rules":[{"rule":"^[0-9]*$","msg":"您输入的内容不符合纯数字规则"}],"rulesType":"number","prepend":"","append":"mmol","cssStyle":"","classStyle":[],"layout":"colItem"}]}`,
					idNo: this.patientCenterInfo.idNo,
					signName: '血糖',
					signTime: this.signTime,
					patientName: this.patientCenterInfo.name,
					patientId: this.$store.state.unit.unionId,
					unitId: this.$store.state.unit.id,
					timeSlot: '晚上'
				};
				SignApi.signRecordSave(commitData).then(res => {
					console.log(res);
					if (res.code == 200) {
						this.$emit('submit', {
							before: this.before,
							after: this.after,
							timeSlot: this.timeSlot
						});
						this.fetchAllBloodRecords();
						this.before = '';
						this.after = '';
						this.getChartData(); // 提交后刷新图表数据
						subscribe();
						setTimeout(() => {
							uni.showToast({
								title: "提交成功"
							});
						}, 700);
					}
				});
			},
		}
	}
</script>

<style lang="scss">
	.sugar {
		padding: 30rpx;
		/* 为内容部分添加底部内边距，避免被底部按钮遮挡 */
		padding-bottom: 150rpx;
	}

	.sugar-current {
		padding: 30rpx;
		background: #FFFFFF;
	}

	.weight-title {
		position: relative;
		z-index: 1;
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 20rpx;

		&::after {
			position: absolute;
			bottom: -10rpx;
			left: 0rpx;
			content: "";
			z-index: -1;
			width: 160rpx;
			height: 20rpx;
			background: linear-gradient(to right, #4f41ac, transparent);
		}
	}

	.sugar-input {
		margin-top: 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;

		input {
			width: 260rpx;
			border-bottom: 1px solid #ddd;
			font-size: 60rpx;
			text-align: center;
			padding-bottom: 10rpx;
			transition: border-color 0.3s ease;

			&:focus {
				border-color: #4f41ac;
			}
		}
	}

	.sugar-warn {
		color: #696969;
		font-size: 28rpx;
		text-align: center;
		margin-top: 10rpx;
	}

	.footer-submit {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		border-top: 1px solid #ddd;
		height: 130rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: #FFFFFF;
		text-align: center;
		z-index: 1000;

		&-button {
			height: 90rpx;
			background: #4f41ac;
			width: 90%;
			color: #FFFFFF;
			text-align: center;
			line-height: 90rpx;
			border-radius: 50rpx;
			transition: background 0.3s ease;

			&:hover {
				background: #3c3191;
			}
		}
	}

	.weight-link {
		display: inline-block;
		margin-left: 20rpx;
		color: #FF6347;
		text-decoration: underline;
		font-size: 28rpx;
	}

	.record-card {
		margin-top: 25rpx;
		background: linear-gradient(135deg, #f9f9f9, #ffffff);
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 25rpx;
		box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
		width: 92%;
		margin-left: auto;
		margin-right: auto;
		transition: all 0.3s ease;
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: center;
	}

	.record-time {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
		width: 100%;
		text-align: left;
	}

	.sugar-info {
            display: flex;
            flex-direction: row; // 修改为水平布局
            align-items: center;
            width: 100%;
	}

	.sugar-info view {
		margin-bottom: 5rpx;
		width: 50%; // 固定宽度
	}

	.blood-sugar-trend {
		padding: 30rpx;
		background: #FFFFFF;
		height: 640rpx;
		margin-top: 30rpx;
		position: relative;
	}

	.weight-during {
		display: flex;
		justify-content: space-around;

		&-one {
			background: #f9f9f9;
			color: #000;
			width: 120rpx;
			height: 60rpx;
			text-align: center;
			line-height: 60rpx;
			border-radius: 30rpx;
			font-size: 28rpx;
		}

		&-active {
			background: #4f41ac;
			color: #FFF;
		}
	}

	.weight-unit {
		position: absolute;
		right: 30rpx;
		top: 30rpx;
		font-size: 32rpx;
		color: #999;
	}

	.chart-time-filter {
		display: flex;
		justify-content: center;
		gap: 20rpx;
		margin-bottom: 30rpx;
		padding: 0 30rpx;
	}

	.filter-tag {
		background: #f9f9f9;
		color: #666;
		padding: 10rpx 20rpx;
		border-radius: 30rpx;
		font-size: 28rpx;
		text-align: center;
		min-width: 80rpx;
		transition: all 0.3s ease;
		border: 1px solid #e0e0e0;
	}

	.filter-tag-active {
		background: #4f41ac;
		color: #fff;
		border-color: #4f41ac;
	}
</style>
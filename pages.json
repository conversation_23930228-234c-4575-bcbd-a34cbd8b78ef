{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页",
				"navigationStyle": "custom"
			}
		},
		// {
		// 	"path": "pages/login/index",
		// 	"style": {
		// 		"navigationBarTitleText": "登录",
		// 		"navigationStyle": "custom"
		// 	}
		// },
		
		{
			"path": "pages/work/index",
			"style": {
				"navigationBarTitleText": "工作台",
				"navigationStyle": "custom"
			}
		},

		{
			"path": "pages/center/index",
			"style": {
				"navigationBarTitleText": "我的",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/forum/index",
			"style": {
				"navigationBarTitleText": "胖友圈",
				"navigationStyle": "custom"
			}
		},
		// {
		// 	"path": "pages/concat/concat",
		// 	"style": {
		// 		"navigationBarTitleText": "在线咨询",
		// 		"enablePullDownRefresh": false,
		// 		"navigationStyle": "custom"
		// 	}
		// },
		{
			"path": "pages/yechat/index",
			"style": {
				"navigationBarTitleText": "在线咨询",
				// "enablePullDownRefresh": false,
				// "navigationBarBackgroundColor": "#b7bbfa",
				// "navigationBarTextStyle": "white",
				"navigationStyle": "custom"
				
				// "navigationStyle": "custom"
			}
		},
		// {
		// 	"path" : "pages/login/agreement",
		// 	"style" : 
		// 	{
		// 		"navigationBarTitleText" : ""
		// 	}
		// },
		{
			"path" : "pages/patients/patients",
			"style" : 
			{
				"navigationBarTitleText" : "患者管理",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		}

	],
	"subPackages": [{
			"root": "pageIndex",
			"pages": [{
					"path": "articleInfo",
					"style": {
						"navigationBarTitleText": "文章详情"
					}
				},
				{
					"path": "ThreeHeightInfo",
					"style": {
						"navigationBarTitleText": "文章详情"
					}
				},
				{
					"path": "login/index",
					"style": {
								"navigationBarTitleText": "登录",
								"navigationStyle": "custom"
					}
				},
				{
					"path": "curseList",
					"style": {
						"navigationBarTitleText": "小课堂列表"
					}
				},
				{
					"path": "articleList",
					"style": {
						"navigationBarTitleText": "文章列表",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "agreement",
					"style": {
						"navigationBarTitleText": ""
					}
				}
				
			]
		},
		{
			"root": "pageWork",
			"pages": [{
					"path": "user/list",
					"style": {
						"navigationBarTitleText": "用户管理",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "user/edit",
					"style": {
						"navigationBarTitleText": "用户详情",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "notice/list",
					"style": {
						"navigationBarTitleText": "通知公告",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "notice/manage",
					"style": {
						"navigationBarTitleText": "公告管理",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "notice/detail",
					"style": {
						"navigationBarTitleText": "公告详情",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "notice/edit",
					"style": {
						"navigationBarTitleText": "公告详情",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "dayCommit/dayCommit",
					"style": {
						"navigationBarTitleText": "每日提交",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "statics/bgm",
					"style": {
						"navigationBarTitleText": "指尖血糖",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "statics/cgm",
					"style": {
						"navigationBarTitleText": "健康报告",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "statics/diet",
					"style": {
						"navigationBarTitleText": "饮食分析",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "screening/high",
					"style": {
						"navigationBarTitleText": "高危筛查",
						"enablePullDownRefresh": true,
						"onReachBottomDistance": 0,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "screening/complication",
					"style": {
						"navigationBarTitleText": "并发症筛查",
						"enablePullDownRefresh": true,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "screening/screenDetail",
					"style": {
						"navigationBarTitleText": "筛查详情",
						"enablePullDownRefresh": true,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "deviceConnect/index",
					"style": {
						"navigationBarTitleText": "设备连接",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "vivaChek/index",
					"style": {
						"navigationBarTitleText": "血糖测量",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "signCommit/commit",
					"style": {
						"navigationBarTitleText": "体征信息提交",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "questions/questionList",
					"style": {
						"navigationBarTitleText": "问题咨询",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "questions/questionEdit",
					"style": {
						"navigationBarTitleText": "问题编辑",
						"navigationStyle": "custom"
					}
				},
				{
					"path" : "foodCategory/foodCategory",
					"style" :
					{
						"navigationBarTitleText" : "饮食选择",
						"navigationStyle": "custom"
					}
				},
				{
					"path" : "foodCommit/foodCommit",
					"style" :
					{
						"navigationBarTitleText" : "饮食记录",
						"navigationStyle": "custom"
					}
				},
				{
					"path" : "nutrAnalysis/nutrAnalysis",
					"style" : 
					{
						"navigationBarTitleText" : ""
					}
				},
				{
					"path" : "nutrRanking/nutrRanking",
					"style" : 
					{
						"navigationBarTitleText" : ""
					}
				},
				{
					"path" : "eatStatics/eatStatics",
					"style" : 
					{
						"navigationBarTitleText" : ""
					}
				},
				{
					"path" : "exams/exams",
					"style" :
					{
						"navigationBarTitleText" : "问卷调查",
						"navigationStyle": "custom"
					}
				},
				{
					"path" : "exams/detail",
					"style" :
					{
						"navigationBarTitleText" : "问卷详情",
						"navigationStyle": "custom"
					}
				},				{
					"path" : "exams/weightLossDetail",
					"style" :
					{
						"navigationBarTitleText" : "问卷详情",
						"navigationStyle": "custom"
					}
				},
				{
					"path" : "exams/patientExams",
					"style" :
					{
						"navigationBarTitleText" : "患者问卷",
						"navigationStyle": "custom"
					}
				},
				{
					"path" : "exams/weightLossExams",
					"style" : 
					{
						"navigationBarTitleText" : "患者问卷"
					}
				},
				{
					"path" : "patientList/patientList",
					"style" :
					{
						"navigationBarTitleText" : "患者审核",
						"navigationStyle": "custom"
					}
				},
				{
					"path" : "patients/patients",
					"style" : 
					{
						"navigationBarTitleText" : "患者列表",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path" : "jzList/jzList",
					"style" : 
					{
						"navigationBarTitleText" : "就诊评价"
					}
				},
				{
					"path" : "pjAdd/pjAdd",
					"style" : 
					{
						"navigationBarTitleText" : "新增评价"
					}
				},
				{
					"path" : "patientReport/patientReport",
					"style" : 
					{
						"navigationBarTitleText" : "患者报告"
					}
				},
				{
					"path" : "evaList/evaList",
					"style" : 
					{
						"navigationBarTitleText" : "阶段评估"
					}
				},
				{
					"path" : "evaAdd/evaAdd",
					"style" : 
					{
						"navigationBarTitleText" : "阶段评估"
					}
				},
				{
					"path" : "artileList/artileList",
					"style" : 
					{
						"navigationBarTitleText" : "文章分享"
					}
				},
				{
					"path" : "dealWarn/dealWarn",
					"style" : 
					{
						"navigationBarTitleText" : "预警处理"
					}
				},
				{
					"path" : "healthEdit/healthEdit",
					"style" : 
					{
						"navigationBarTitleText" : "",
						"mp-weixin": {
							"pageOrientation": "landscape"
						}

					}
				},
				{
					"path" : "healths/healths",
					"style" : 
					{
						"navigationBarTitleText" : ""
					}
				},
				{
					"path" : "healthFormsAndTemp/healthFormsAndTemp",
					"style" : 
					{
						"navigationBarTitleText" : ""
					}
				},
				{
					"path" : "healthFormsAndInfo/healthFormsAndInfo",
					"style" :
					{
						"navigationBarTitleText" : "患者信息",
						"navigationStyle": "custom"
					}
				},
				{
					"path" : "bhSearchPage/bhSearchPage",
					"style" : 
					{
						"navigationBarTitleText" : ""
					}
				},
				{
					"path" : "patientPjAdd/patientPjAdd",
					"style" : 
					{
						"navigationBarTitleText" : "就诊报告详情"
					}
				},
				{
					"path" : "diabetes/diabetes",
					"style" :
					{
						"navigationBarTitleText" : "糖尿病课堂",
						"navigationStyle": "custom"
					}
				},
				{
					"path" : "threeHighs/threeHighs",
					"style" : 
					{
						"navigationBarTitleText" : "三高课堂"
					}
				},
				{
					"path" : "guarantee/guarantee",
					"style" : 
					{
						"navigationBarTitleText" : "泉州保障"
					}
				},
				{
					"path" : "our/our",
					"style" :
					{
						"navigationBarTitleText" : "关于我们",
						"navigationStyle": "custom"
					}
				},
				{
					"path" : "diabetesList/diabetesList",
					"style" : 
					{
						"navigationBarTitleText" : "课堂文章"
					}
				},
				{
					"path" : "fatClass/fatClass",
					"style" :
					{
						"navigationBarTitleText" : "减重课堂",
						"navigationStyle": "custom"
					}
				},
				{
					"path" : "AiScheme/AiScheme",
					"style" : 
					{
						"navigationBarTitleText" : "干预方案"
					}
				},
				{
					"path" : "forum/publish",
					"style" : 
					{
						"navigationBarTitleText" : "发布动态",
						"navigationStyle": "custom"
					}
				},
				{
					"path" : "forum/unreadLikes",
					"style" : 
					{
						"navigationBarTitleText" : "点赞列表"
					}
				},
				{
					"path" : "forum/momentDetail",
					"style" : 
					{
						"navigationBarTitleText" : "动态详情"
					}
				}
						
			]
		},
		{
			"root": "pageCenter",
			"pages": [{
					"path": "profile",
					"style": {
						"navigationBarTitleText": "个人资料",
						"navigationStyle": "custom",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "build",
					"style": {
						"navigationBarTitleText": "建档",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "qrCode/qrCode",
					"style": {
						"navigationBarTitleText": "身份二维码",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "member/member",
					"style": {
						"navigationBarTitleText": "会员购买",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "selfInfo/index",
					"style": {
						"navigationBarTitleText": "个人信息",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "moodStatus/index",
					"style": {
						"navigationBarTitleText": "我的状态",
						"enablePullDownRefresh": false,
						"navigationStyle": "custom"
					}
				},
				{
					"path": "common/markOrIDPage/index",
					
					"style": {
						"navigationBarTitleText": "我的昵称",
						"enablePullDownRefresh": false
						// "navigationStyle": "custom"
					}
				},
				{
					"path" : "myTeam/myTeam",
					"style" :
					{
						"navigationBarTitleText" : "",
						"navigationStyle": "custom"
					}
				},
				{
					"path" : "note/note",
					"style" : 
					{
						"navigationBarTitleText" : ""
					}
				},
				{
					"path" : "noteAdd/noteAdd",
					"style" : 
					{
						"navigationBarTitleText" : ""
					}
				},
				{
					"path" : "patientList/patientList",
					"style" : 
					{
						"navigationBarTitleText" : "",
						"navigationStyle": "custom"
					}
				},
				{
					"path" : "notice/notice",
					"style" :
					{
						"navigationBarTitleText" : "消息通知",
						"navigationStyle": "custom"
					}
				},
				{
					"path" : "warnInfo/warnInfo",
					"style" : 
					{
						"navigationBarTitleText" : ""
					}
				},
				{
					"path" : "updatePassWord/updatePassWord",
					"style" :
					{
						"navigationBarTitleText" : "修改密码",
						"navigationStyle": "custom"
					}
				}
			]
		},
		// {
		// 	"root": "pageChat",
		// 	"pages": [{
		// 		"path": "chating/index",
		// 		"style": {
		// 			"navigationBarTitleText": "聊天",
		// 			"navigationStyle": "custom"
		// 		}
		// 	}]
		// },
		{
			"root": "pageYeChat",
			"pages": [
				{
					"path": "chat/chat1",
					"style": {
						"navigationBarTitleText": "",
						"navigationBarBackgroundColor": "#b7bbfa",
						"navigationBarTextStyle": "white",
						"backgroundColor": "#f7f7f7",
						"enablePullDownRefresh": true,
						"app-plus": {
							"softinputmode": "adjustPan",
							"safeAreaInsets": {
								"bottom": true
							}
						},
						"mp-weixin": {
							"safeAreaInsets": {
								"bottom": true
							}
						},
						"navigationStyle": "custom"
					}
				},
				// {
				// 	"path": "chat/chat2",
				// 	"style": {
				// 		"enablePullDownRefresh": false,
				// 		"disableScroll": true,
				// 		"navigationStyle": "custom",
				// 		"navigationBarTextStyle": "black"
				// 	}
				// },
				{
					"path": "contact/applyList",
					"style":{
					    "navigationBarTitleText": "好友申请",
					    "enablePullDownRefresh": false
					}
				},
				{
					"path": "contact/groupList",
					"style":{
					    "navigationBarTitleText": "我的群聊",
					    "enablePullDownRefresh": false
					}
				},
				{
					"path": "contact/list",
					"style": {
						"navigationBarTitleText": "好友",
						"navigationBarBackgroundColor": "#b7bbfa",
						"backgroundColor": "#f7f7f7",
						"navigationBarTextStyle": "white"
					}
				},
				{
					"path": "contact/searchUser",
					"style": {
						"navigationBarTitleText": "搜索用户",
						"navigationBarBackgroundColor": "#b7bbfa",
						"navigationBarTextStyle": "white",
						"backgroundColor": "#f7f7f7",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "index/searchGroup",
					"style": {
						"navigationBarTitleText": "搜索群组",
						"navigationBarBackgroundColor": "#b7bbfa",
						"navigationBarTextStyle": "white",
						"backgroundColor": "#f7f7f7",
						"enablePullDownRefresh": false
					}
				},
				{
					"path": "index/createGroup",
					"style": {
						"navigationBarTitleText": "创建群聊",
						"enablePullDownRefresh": false
					}
				},
				{
					"path" : "chat/groupInfo",
					"style" : 
					{
						"navigationBarTitleText" : "聊天信息",
						"navigationBarBackgroundColor": "#b7bbfa",
						"navigationBarTextStyle": "white",
						"backgroundColor": "#f7f7f7",
						"enablePullDownRefresh": false
					}
				}
			]
		}
	
	],
	// "preloadRule": {
		// "pages/index/index":{
		// 	"network": "all",
		// 	"packages": ["pageIndex"]
		// },
		// "pages/work/index": {
		// 	"network": "all",
		// 	"packages": ["pageWork"]
		// },
		// "pages/center/index": {
		// 	"network": "all",
		// 	"packages": ["pageCenter"]
		// },
		// "pages/concat/concat": {
		// 	"network": "all",
		// 	"packages": ["pageChat"]
		// }
	// },
	
	"tabBar": {
		"color": "#909399",
		"selectedColor": "#000000",
		"borderStyle": "black",
		"backgroundColor": "#ffffff",
		"list": [
			{
				"pagePath": "pages/index/index"
			},
			{
				// "pagePath": "pages/concat/concat"
				"pagePath": "pages/yechat/index"
			}, 
			{
				"pagePath": "pages/forum/index"
			},
			{
				"pagePath": "pages/center/index"
			},
			{
				"pagePath": "pages/patients/patients"
			}
		]
	},

	// "tabBar": {
	// 	"color": "#909399",
	// 	"selectedColor": "#000000",
	// 	"borderStyle": "black",
	// 	"backgroundColor": "#ffffff",
	// 	"list": [

	// 		{
	// 			"pagePath": "pages/index/index",
	// 			"iconPath": "static/img/tabbar/home.png",
	// 			"selectedIconPath": "static/img/tabbar/home_art.png",
	// 			"text": "减重科普"
	// 		},
	// 		{
	// 			"pagePath": "pages/concat/concat",
	// 			"iconPath": "static/img/tabbar/chat.png",
	// 			"selectedIconPath": "static/img/tabbar/chat_art.png",
	// 			"text": "在线咨询"
	// 		}, 
	// 		{
	// 			"pagePath": "pages/work/index",
	// 			"iconPath": "static/img/tabbar/center.png",
	// 			"selectedIconPath": "static/img/tabbar/center_art.png",
	// 			"text": "健康管理"
	// 		},
	// 		{
	// 			"pagePath": "pages/center/index",
	// 			"iconPath": "static/img/tabbar/user.png",
	// 			"selectedIconPath": "static/img/tabbar/user_art.png",
	// 			"text": "我的信息"
	// 		}
	// 	]
	// },

	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8",
		"pageOrientation": "auto",
		"app-plus": {
			"softinputMode": "adjustResize"
		}
	},
	"easycom": {
		"^u-(.*)": "@/uni_modules/uview-ui/components/u-$1/u-$1.vue"
	}
}
<template>
	<view class="container">
		<view class="header">
			<text class="title">图片裁剪测试</text>
			<text class="subtitle">测试背景透明效果</text>
		</view>

		<view class="content">
			<button @click="selectImage" class="select-btn">选择图片进行裁剪</button>

			<view class="demo-content">
				<text class="demo-title">背景内容示例</text>
				<view class="demo-card">
					<text class="card-title">这是一个卡片</text>
					<text class="card-content">当裁剪界面打开时，这些内容应该仍然可见，只是稍微变暗</text>
				</view>

				<view class="demo-list">
					<view class="list-item" v-for="(item, index) in demoList" :key="index">
						<text class="item-text">{{ item }}</text>
					</view>
				</view>
			</view>

			<view v-if="selectedImage" class="image-preview">
				<text class="preview-title">预览图片:</text>
				<image :src="selectedImage" mode="aspectFit" class="preview-img"></image>
			</view>

			<view v-if="croppedImage" class="cropped-result">
				<text class="result-title">裁剪结果:</text>
				<image :src="croppedImage" mode="aspectFit" class="result-img"></image>
			</view>
		</view>

		<!-- 裁剪组件 -->
		<cropper
			v-if="showCropper"
			:avatarSrc="selectedImage"
			selWidth="300rpx"
			selHeight="300rpx"
			expWidth="300"
			expHeight="300"
			@upload="onCropComplete"
			@close="onCropClose"
		></cropper>
	</view>
</template>

<script>
import cropper from '@/components/cropper/cropper.vue'

export default {
	components: {
		cropper
	},
	data() {
		return {
			selectedImage: '',
			croppedImage: '',
			showCropper: false,
			demoList: [
				'列表项目 1 - 测试背景内容',
				'列表项目 2 - 裁剪时应该可见',
				'列表项目 3 - 只是稍微变暗',
				'列表项目 4 - 不会完全变黑',
				'列表项目 5 - 保持可读性',
				'列表项目 6 - 按钮区域也是半透明',
				'列表项目 7 - 整体效果更协调'
			]
		}
	},
	methods: {
		selectImage() {
			uni.chooseImage({
				count: 1,
				sizeType: ['original', 'compressed'],
				sourceType: ['camera', 'album'],
				success: (res) => {
					this.selectedImage = res.tempFilePaths[0];
					this.showCropper = true;
				},
				fail: (err) => {
					console.error('选择图片失败:', err);
					uni.showToast({
						title: '选择图片失败',
						icon: 'none'
					});
				}
			});
		},
		
		onCropComplete(result) {
			console.log('裁剪完成:', result);
			this.croppedImage = result.path;
			this.showCropper = false;
			uni.showToast({
				title: '裁剪成功',
				icon: 'success'
			});
		},
		
		onCropClose() {
			console.log('关闭裁剪器');
			this.showCropper = false;
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	padding: 20rpx;
	min-height: 100vh;
	background-color: #f5f5f5;
}

.header {
	text-align: center;
	padding: 40rpx 0;

	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
	}

	.subtitle {
		font-size: 24rpx;
		color: #666;
	}
}

.content {
	padding: 20rpx;
}

.select-btn {
	width: 100%;
	height: 80rpx;
	background-color: #007aff;
	color: white;
	border: none;
	border-radius: 10rpx;
	font-size: 32rpx;
	margin-bottom: 40rpx;
}

.demo-content {
	margin-bottom: 40rpx;

	.demo-title {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
		margin-bottom: 20rpx;
		display: block;
	}

	.demo-card {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 30rpx;
		border-radius: 15rpx;
		margin-bottom: 30rpx;

		.card-title {
			color: white;
			font-size: 32rpx;
			font-weight: bold;
			display: block;
			margin-bottom: 15rpx;
		}

		.card-content {
			color: rgba(255, 255, 255, 0.9);
			font-size: 26rpx;
			line-height: 1.5;
			display: block;
		}
	}

	.demo-list {
		.list-item {
			background-color: white;
			padding: 25rpx 30rpx;
			margin-bottom: 15rpx;
			border-radius: 10rpx;
			border-left: 6rpx solid #007aff;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

			.item-text {
				color: #333;
				font-size: 28rpx;
			}
		}
	}
}

.image-preview, .cropped-result {
	margin-bottom: 40rpx;
	
	.preview-title, .result-title {
		display: block;
		font-size: 28rpx;
		color: #666;
		margin-bottom: 20rpx;
	}
	
	.preview-img, .result-img {
		width: 100%;
		height: 400rpx;
		border-radius: 10rpx;
		border: 2rpx solid #ddd;
	}
}
</style>

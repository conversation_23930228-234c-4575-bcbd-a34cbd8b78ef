<template name="cropper">
	<view>
		<image :src="imgSrc.imgSrc" class="my-avatar" mode="aspectFill" :style="imgStyle"></image>

		<!-- 背景遮罩层 -->
		<view v-show="showCropper" class="background-mask"></view>

		<!-- 上传图片 -->
		<canvas canvas-id="avatar-canvas" id="avatar-canvas" class="my-canvas" :style="{top: styleTop, height: cvsStyleHeight}"
		 disable-scroll="false"></canvas>
		<!-- 截取边框 -->
		<canvas canvas-id="oper-canvas" id="oper-canvas" class="oper-canvas" :style="{top: styleTop, height: cvsStyleHeight}"
		 disable-scroll="false" @touchstart="fStart" @touchmove="fMove" @touchend="fEnd"></canvas>
		<view class="oper-wrapper" v-show="showCropper">
			<view class="btn-wrapper">
				<view @click="fClose" hover-class="hover">取消</view>
				<view @click="fUpload" hover-class="hover">选取</view>
			</view>
		</view>
	</view>
</template>

<script>
	const tabHeight = 70;
	export default {
		name: "cropper",
		data() {
			return {
				cvsStyleHeight: '0px',
				styleDisplay: 'none',
				styleTop: '-10000px',
				prvTop: '-10000px',
				imgStyle: {},
				selStyle: {},
				showOper: true,
				showCropper: false,
				imgSrc: {
					imgSrc: ''
				},
				qlty: 0.9,
				postWidthFirst: {},
			};
		},
		watch: {
			avatarSrc() {
				this.imgSrc.imgSrc = this.avatarSrc;
				// 当传入新的图片时，自动初始化裁剪
				if (this.avatarSrc) {
					this.initCropperWithImage(this.avatarSrc);
				}
			}
		},
		props: {
			avatarSrc: '',
			avatarStyle: '',
			selWidth: '',
			selHeight: '',
			expWidth: '',
			expHeight: '',
			minScale: '',
			maxScale: '',
			canScale: '',
			noTop: '',
			quality: '',
			index: ''
		},
		created() {
			this.qlty = parseInt(this.quality) || 0.9;
			this.imgSrc.imgSrc = this.avatarSrc;
			this.letScale = this.canScale === 'false' ? 0 : 1;
			this.indx = this.index || undefined;
			this.mnScale = this.minScale || 0.3;
			this.mxScale = this.maxScale || 4;
			this.noBar = this.noTop ? false : true;
		},
		mounted() {
			console.log('组件已挂载，开始初始化canvas');

			// 在mounted中创建canvas上下文，确保DOM已经渲染
			this.ctxCanvas = uni.createCanvasContext('avatar-canvas', this);
			this.ctxCanvasOper = uni.createCanvasContext('oper-canvas', this);

			if (!!this.noBar) {
				this.moreHeight = 0;
				this.fWindowResize();
			} else {
				uni.showTabBar({
					complete: (res) => {
						this.moreHeight = (res.errMsg === 'showTabBar:ok') ? 50 : 0;
						this.fWindowResize();
					}
				});
			}

			// 如果传入了图片，延迟初始化裁剪
			if (this.avatarSrc) {
				setTimeout(() => {
					this.initCropperWithImage(this.avatarSrc);
				}, 200);
			}
		},
		methods: {
			// 使用传入的图片初始化裁剪器
			initCropperWithImage(imagePath) {
				let self = this;
				console.log('开始初始化裁剪器，图片路径:', imagePath);

				// 立即显示裁剪界面
				this.showCropper = true;

				uni.showLoading({
					title: '加载中...',
					mask: true
				});

				this.imgPath = imagePath;

				// 添加延迟确保canvas已经创建
				setTimeout(() => {
					uni.getImageInfo({
						src: imagePath,
						success: r => {
							console.log('获取图片信息成功:', r);
							this.imgWidth = r.width;
							this.imgHeight = r.height;
							this.path = imagePath;

							if (!this.hasSel) {
								let style = this.selStyle || {};
								if (this.selWidth && this.selHeight) {
									let selWidth = this.selWidth.indexOf('rpx') >= 0 ? parseInt(this.selWidth) * this.pxRatio :
										parseInt(this.selWidth),
										selHeight = this.selHeight.indexOf('rpx') >= 0 ? parseInt(this.selHeight) * this.pxRatio :
										parseInt(this.selHeight);
									style.width = parseInt(selWidth);
									style.height = parseInt(selHeight);
									style.top = parseInt((this.windowHeight - style.height - 220) / 2);
									style.left = parseInt((this.windowWidth - style.width) / 2);
								} else {
									uni.hideLoading();
									uni.showModal({
										title: '裁剪框的宽或高没有设置',
										showCancel: false
									})
									return;
								}
								this.selStyle = style;
							}

							// 确保在主线程中执行
							this.$nextTick(() => {
								try {
									console.log('开始绘制初始化');
									self.fDrawInit(true);
								} catch (error) {
									console.error('绘制初始化失败:', error);
									uni.hideLoading();
									uni.showToast({
										title: '初始化失败',
										icon: 'none'
									});
								}
							});
						},
						fail: (error) => {
							console.error('获取图片信息失败:', error);
							uni.hideLoading();
							uni.showToast({
								title: "获取图片信息失败",
								icon: 'none',
								duration: 2000,
							})
						}
					});
				}, 100); // 延迟100ms确保canvas已创建
			},
			fWindowResize() {
				let sysInfo = uni.getSystemInfoSync();
				this.platform = sysInfo.platform;
				this.pixelRatio = sysInfo.pixelRatio;
				this.windowWidth = sysInfo.windowWidth;
				this.windowHeight = sysInfo.windowHeight;

				// 设置canvas高度，减去按钮区域高度
				let buttonAreaHeight = 220;
				this.cvsStyleHeight = (this.windowHeight - buttonAreaHeight) + 'px';

				// #ifdef H5
				this.drawTop = 0;
				// #endif

				this.pxRatio = this.windowWidth / 750;

				let style = this.avatarStyle;
				this.imgStyle = style;
				this.expWidth && (this.exportWidth = this.expWidth.indexOf('rpx') >= 0 ? parseInt(this.expWidth) * this.pxRatio :
					parseInt(this.expWidth));
				this.expHeight && (this.exportHeight = this.expHeight.indexOf('rpx') >= 0 ? parseInt(this.expHeight) * this.pxRatio :
					parseInt(this.expHeight));

				if (this.styleDisplay === 'flex') {
					this.fDrawInit(true);
				}
				this.fHideImg();
			},
			fSelect() {
        let self=this;
				if (this.fSelecting) return;
				this.fSelecting = true;
				setTimeout(() => {
					this.fSelecting = false;
				}, 500);

				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['camera', 'album'],
					success: (r) => {
						uni.showLoading({
							mask: true
						});
						let path = this.imgPath = r.tempFilePaths[0];
						uni.getImageInfo({
							src: path,
							success: r => {
								this.imgWidth = r.width;
								this.imgHeight = r.height;
								this.path = path;
								if (!this.hasSel) {
									let style = this.selStyle || {};
									if (this.selWidth && this.selHeight) {
										let selWidth = this.selWidth.indexOf('rpx') >= 0 ? parseInt(this.selWidth) * this.pxRatio :
											parseInt(this.selWidth),
											selHeight = this.selHeight.indexOf('rpx') >= 0 ? parseInt(this.selHeight) * this.pxRatio :
											parseInt(this.selHeight);
										style.width = parseInt(selWidth);
										style.height = parseInt(selHeight);
										style.top = parseInt((this.windowHeight - style.height - tabHeight) / 2);
										style.left = parseInt((this.windowWidth - style.width) / 2);
									} else {
										uni.showModal({
											title: '裁剪框的宽或高没有设置',
											showCancel: false
										})
										return;
									}
									this.selStyle = style;

								}

								if (!!self.noBar) {
									self.fDrawInit(true);
								} else {
									uni.hideTabBar({
										complete: () => {
											self.fDrawInit(true);
										}
									});
								}
							},
							fail: () => {
								uni.showToast({
									title: "error3",
									duration: 2000,
								})
							},
							complete() {
								uni.hideLoading();
							}
						});
					}
				})
			},
			fUpload() {
				if (this.fUploading) return;
				this.fUploading = true;
				setTimeout(() => {
					this.fUploading = false;
				}, 1000)

				let style = this.selStyle,
					x = parseInt(style.left),
					y = parseInt(style.top),
					width = parseInt(style.width),
					height = parseInt(style.height),
					expWidth = this.exportWidth || width,
					expHeight = this.exportHeight || height;

				// #ifdef H5
				// x *= this.pixelRatio;
				// y *= this.pixelRatio;
				// expWidth = width;
				// expHeight = height;
				// #endif

				this.styleDisplay = 'none';
				this.styleTop = '-10000px';
				this.hasSel = false;
				this.fHideImg();
				uni.canvasToTempFilePath({
					x: x,
					y: y,
					width: width,
					height: height,
					destWidth: expWidth,
					destHeight: expHeight,
					canvasId: 'avatar-canvas',
					fileType: 'png',
					quality: this.qlty,
					success: (r) => {
						r = r.tempFilePath;
						// #ifdef H5
						this.btop(r).then((r) => {
							if (this.exportWidth && this.exportHeight) {
								let ctxCanvas = this.ctxCanvas;
								expWidth = this.exportWidth,
									expHeight = this.exportHeight;

								ctxCanvas.drawImage(r, 0, 0, expWidth, expHeight);
								ctxCanvas.draw(false, () => {
									uni.canvasToTempFilePath({
										x: 0,
										y: 0,
										width: expWidth,
										height: expHeight,
										destWidth: expWidth,
										destHeight: expHeight,
										canvasId: 'avatar-canvas',
										fileType: 'png',
										quality: this.qlty,
										success: (r) => {
											r = r.tempFilePath;
											this.btop(r).then((r) => {
												this.$emit("upload", {
													avatar: this.imgSrc,
													path: r,
													index: this.indx
												});
											});
										},
										fail: () => {
											uni.showToast({
												title: "error0",
												duration: 2000,
											})
										}
									});
								});
							} else {
								this.$emit("upload", {
									avatar: this.imgSrc,
									path: r,
									index: this.indx
								});
							}
						})
						// #endif
						// #ifndef H5
						this.$emit("upload", {
							avatar: this.imgSrc,
							path: r,
							index: this.indx
						});
						// #endif
					},
					fail: (res) => {
						uni.showToast({
							title: "error1",
							duration: 2000,
						})
					},
					complete: () => {
						this.noBar || uni.showTabBar();
					}
				}, this);
			},
			fDrawInit(ini = false) {
				let allWidth = this.windowWidth,
					allHeight = this.windowHeight,
					imgWidth = this.imgWidth,
					imgHeight = this.imgHeight,
					imgRadio = imgWidth / imgHeight,
					useWidth = allWidth,
					useHeight = allHeight - 220, // 减去底部按钮区域高度(120px位置 + 100px高度)
					selWidth = parseInt(this.selStyle.width),
					selHeight = parseInt(this.selStyle.height);

				this.fixWidth = 0;
				this.fixHeight = 0;

				if (this.fixWidth) {
					useWidth = selWidth;
					useHeight = useWidth / imgRadio;
				} else if (this.fixHeight) {
					useHeight = selHeight;
					useWidth = useHeight * imgRadio;
				} else if (imgRadio < 1) {
					useWidth = selWidth;
					useHeight = parseInt(useWidth / imgRadio);
				} else {
					useHeight = selHeight;
					useWidth = parseInt(useHeight * imgRadio);
				}

				this.scaleSize = 1;
				this.rotateDeg = 0;
				this.posWidth = parseInt((allWidth - useWidth) / 2);
				this.posHeight = parseInt((allHeight - useHeight - 220) / 2); // 调整位置计算
				this.useWidth = useWidth;
				this.useHeight = useHeight;
				let style = this.selStyle,
					left = parseInt(style.left),
					top = parseInt(style.top),
					width = parseInt(style.width),
					height = parseInt(style.height),
					canvas = this.canvas,
					canvasOper = this.canvasOper,
					ctxCanvas = this.ctxCanvas,
					ctxCanvasOper = this.ctxCanvasOper;
				ctxCanvasOper.setFillStyle('rgba(0,0,0, 0.2)'); // 进一步降低遮罩透明度
				ctxCanvasOper.fillRect(0, 0, this.windowWidth, top);
				ctxCanvasOper.fillRect(0, top, left, height);
				ctxCanvasOper.fillRect(0, top + height, this.windowWidth, this.windowHeight - height - 220 - top);
				ctxCanvasOper.fillRect(left + width, top, this.windowWidth - width - left, height);
				ctxCanvasOper.setLineWidth(1);
				ctxCanvasOper.setStrokeStyle('rgba(255, 255, 255,1)'); //细线的颜色
				ctxCanvasOper.strokeRect(left, top, width, height);
       	// #ifdef H5
        ctxCanvasOper.draw();
        // #endif
				ctxCanvasOper.setLineWidth(3);
				ctxCanvasOper.setStrokeStyle('rgba(255, 255, 255, 1)'); //粗线的颜色
				ctxCanvasOper.moveTo(left + 20, top);
				ctxCanvasOper.lineTo(left, top);
				ctxCanvasOper.lineTo(left, top + 20);
				ctxCanvasOper.moveTo(left + width - 20, top);
				ctxCanvasOper.lineTo(left + width, top);
				ctxCanvasOper.lineTo(left + width, top + 20);
				ctxCanvasOper.moveTo(left + 20, top + height);
				ctxCanvasOper.lineTo(left, top + height);
				ctxCanvasOper.lineTo(left, top + height - 20);
				ctxCanvasOper.moveTo(left + width - 20, top + height);
				ctxCanvasOper.lineTo(left + width, top + height);
				ctxCanvasOper.lineTo(left + width, top + height - 20);
				ctxCanvasOper.stroke();
				this.postFirst = {
					left: left,
					top: top,
					width: width,
					height: selWidth,
					posWidth: this.posWidth,
					posHeight: this.posHeight
				};
				// 先设置显示状态，确保界面能显示
				if (ini) {
					this.styleDisplay = 'flex';
					this.styleTop = '0';
					console.log('设置显示状态:', this.styleDisplay, this.styleTop);
				}

          // #ifdef MP-WEIXIN
				ctxCanvasOper.draw(false, () => {
					if (ini) {
						// 不设置黑色背景，保持透明
						this.fDrawImage();
						uni.hideLoading();
						console.log('微信小程序绘制完成');
					}
				});
        // #endif
        // #ifdef H5
        ctxCanvasOper.draw(true, () => {
        	if (ini) {
        		// 不设置黑色背景，保持透明
        		this.fDrawImage();
        		uni.hideLoading();
        		console.log('H5绘制完成');
        	}
        });
        // #endif

				this.$emit("avtinit");
			},
			fDrawImage() {
				let tm_now = Date.now();
				if ((tm_now - this.drawTm )< 20) return;
				this.drawTm = tm_now;
				let ctxCanvas = this.ctxCanvas;

				// 清除canvas内容
				let buttonAreaHeight = 220; // 按钮区域高度
				let canvasHeight = this.windowHeight - buttonAreaHeight;

				// 清除整个canvas
				ctxCanvas.clearRect(0, 0, this.windowWidth, canvasHeight);

				// 保存当前状态
				ctxCanvas.save();

				//中心点坐标
				ctxCanvas.translate(this.posWidth + this.useWidth / 2, this.posHeight + this.useHeight / 2);
				//比例缩放
				ctxCanvas.scale(this.scaleSize, this.scaleSize);
				ctxCanvas.drawImage(this.imgPath, -this.useWidth / 2, -this.useHeight / 2, this.useWidth, this.useHeight);

				// 恢复状态
				ctxCanvas.restore();

				ctxCanvas.draw(false);
			},
			fHideImg() {
				this.prvImg = '';
				this.prvTop = '-10000px';
				this.showOper = true;
				this.prvImgData = null;
				this.target = null;
			},
			fClose() {
				this.showCropper = false;
				this.styleDisplay = 'none';
				this.styleTop = '-10000px';
				this.hasSel = false;
				this.fHideImg();
				this.noBar || uni.showTabBar();
				// 通知父组件关闭裁剪器
				this.$emit('close');
			},
      // #ifdef MP-WEIXIN
			fStart(e) {
				let touches = e.touches,
					touch0 = touches[0],
					touch1 = touches[1];

				this.touch0 = touch0;
				this.touch1 = touch1;

				if (touch1) {
         let x = touch1.x - touch0.x,
         	y = touch1.y - touch0.y;
         this.fgDistance = Math.sqrt(x * x + y * y);

				}
			},
      // #endif
      // #ifdef H5
      fStart(e) {
      	let touches = e.touches,
      		touch0 = touches[0],
      		touch1 = touches[1];

      	this.touch0 = touch0;
      	this.touch1 = touch1;

      	if (touch1) {
            let x = touch1.clientX - touch0.clientX,
            	y = touch1.clientY - touch0.clientY;
            this.fgDistance = Math.sqrt(x * x + y * y);
      	}
      },
      // #endif
      // #ifdef MP-WEIXIN
			fMove(e) {
				let touches = e.touches,
					touch0 = touches[0],
					touch1 = touches[1];

				if (touch1) {
					let x = touch1.x - touch0.x,
						y = touch1.y - touch0.y,
						fgDistance = Math.sqrt(x * x + y * y),
						scaleSize = 0.005 * (fgDistance - this.fgDistance),
						beScaleSize = this.scaleSize + scaleSize;

					do {
						if (!this.letScale) break;
						if (beScaleSize < this.mnScale) break;
						if (beScaleSize > this.mxScale) break;
						this.scaleSize = beScaleSize;
					} while (0);
					this.fgDistance = fgDistance;

					if (touch1.x !== touch0.x && this.letRotate) {
						x = (this.touch1.y - this.touch0.y) / (this.touch1.x - this.touch0.x);
						y = (touch1.y - touch0.y) / (touch1.x - touch0.x);
						this.rotateDeg += Math.atan((y - x) / (1 + x * y)) * 180 / Math.PI;
						this.touch0 = touch0;
						this.touch1 = touch1;
					}

					this.fDrawImage();
				} else if (this.touch0) {
					let x = touch0.x - this.touch0.x,
						y = touch0.y - this.touch0.y,
						beX = this.posWidth + x,
						beY = this.posHeight + y;
						if (Math.abs(x) < 100 && !this.lckWidth) this.posWidth = beX;
						if (Math.abs(y) < 100 && !this.lckHeight) this.posHeight = beY;
					this.touch0 = touch0;
					this.fDrawImage();
				}
			},
        // #endif
      	// #ifdef H5
        fMove(e) {
        	let touches = e.touches,
        		touch0 = touches[0],
        		touch1 = touches[1];

        	if (touch1) {
        		let x = touch1.clientX - touch0.clientX,
        			y = touch1.clientY - touch0.clientY,
        			fgDistance = Math.sqrt(x * x + y * y),
        			scaleSize = 0.005 * (fgDistance - this.fgDistance),
        			beScaleSize = this.scaleSize + scaleSize;

        		do {
        			if (!this.letScale) break;
        			if (beScaleSize < this.mnScale) break;
        			if (beScaleSize > this.mxScale) break;
        			this.scaleSize = beScaleSize;
        		} while (0);
        		this.fgDistance = fgDistance;

        		if (touch1.x !== touch0.x && this.letRotate) {
        			x = (this.touch1.clientY - this.touch0.clientY) / (this.touch1.clientX - this.touch0.clientX);
        			y = (touch1.clientY - touch0.clientY) / (touch1.clientX - touch0.clientX);
        			this.rotateDeg += Math.atan((y - x) / (1 + x * y)) * 180 / Math.PI;
        			this.touch0 = touch0;
        			this.touch1 = touch1;
        		}

        		this.fDrawImage();
        	} else if (this.touch0) {
            let x=touch0.clientX - this.touch0.clientX,
            y=touch0.clientY - this.touch0.clientY,
        		// let x = touch0.x - this.touch0.x,
        		// 	y = touch0.y - this.touch0.y,
        			beX = this.posWidth + x,
        			beY = this.posHeight + y;
        			if (Math.abs(x) < 100 && !this.lckWidth) this.posWidth = beX;
        			if (Math.abs(y) < 100 && !this.lckHeight) this.posHeight = beY;
        		this.touch0 = touch0;
        		this.fDrawImage();
        	}
        },
        	// #endif
			async fEnd(e) {
				let self = this;
				let touches = e.touches,
					touch0 = touches && touches[0],
					touch1 = touches && touches[1];
				if (self.scaleSize < 1) {
					let style = self.selStyle;
					let imgRadio = self.imgWidth / self.imgHeight;
					//高长宽短
					if (imgRadio < 1 && self.scaleSize * self.useWidth < style.width) {
						self.posWidth = style.left;
						self.scaleSize = 1
						setTimeout(function() {
							self.fDrawImage();
						}, 100)
					} else if (self.scaleSize * self.useHeight < style.width) {
						//高短宽长
						self.posHeight = style.top;
						self.scaleSize = 1
						setTimeout(function() {
							self.fDrawImage();
						}, 100)
					}
				} else if (this.scaleSize == 1) {
					let endWidth = this.posWidth - this.postFirst.posWidth,
						firstWidth = this.postFirst.left - this.postFirst.posWidth;
					let endHeight = this.posHeight - this.postFirst.posHeight,
						firstHigth = this.postFirst.top - this.postFirst.posHeight;
					if (endWidth > 0 && this.posWidth > this.postFirst.left) {
						//右滑动过长
						this.posWidth = this.postFirst.left;
					} else if (endWidth < 0 && endWidth < firstWidth) {
						//左滑动过长
						this.posWidth = -this.postFirst.left + this.postFirst.posWidth * 2;
					}

					if (endHeight < 0 && this.posHeight < this.postFirst.top) {
						//上滑动过长
						this.posHeight = -this.postFirst.top + this.postFirst.posHeight * 2 ;
					} else if (endHeight > 0 && endHeight > firstHigth) {
						//下滑动过长
						this.posHeight = this.postFirst.top;
					}
					setTimeout(function() {
						self.fDrawImage();
					}, 100);

				}
				if (touch0) {
					this.touch0 = touch0;
				} else {
					this.touch0 = null;
					this.touch1 = null;
				}
			},
			btop(base64) {
				return new Promise(function(resolve, reject) {
					var arr = base64.split(','),
						mime = arr[0].match(/:(.*?);/)[1],
						bstr = atob(arr[1]),
						n = bstr.length,
						u8arr = new Uint8Array(n);
					while (n--) {
						u8arr[n] = bstr.charCodeAt(n);
					}
					return resolve((window.URL || window.webkitURL).createObjectURL(new Blob([u8arr], {
						type: mime
					})));
				});
			},
		}
	}
</script>

<style lang="less">
	.background-mask {
		position: fixed !important;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.15); /* 稍微增加一点透明度，让效果更明显 */
		z-index: 50; /* 在canvas下面，但在原始内容上面 */
	}

	.my-canvas {
		display: flex;
		position: fixed !important;
		background: transparent; /* 改为透明背景 */
		left: 0;
		top: 0;
		z-index: 100;
		width: 100%;
		height: calc(100vh - 220px); /* 减去按钮区域高度，避免覆盖按钮 */
	}

	.my-avatar {
		display: none;
	}

	.oper-canvas {
		display: flex;
		position: fixed !important;
		left: 0;
		top: 0;
		z-index: 101;
		width: 100%;
		height: calc(100vh - 220px); /* 减去按钮区域高度，避免覆盖按钮 */
	}

	.oper-wrapper {
		height: 100px;
		position: fixed !important;
		box-sizing: border-box;
		width: 100%;
		left: 0;
		bottom: 120px; /* 向上移动120px，避免被底部菜单遮挡 */
		z-index: 999; /* 提高z-index确保按钮始终在最上层 */
		flex-direction: row;
		display: flex !important;
		pointer-events: auto; /* 确保按钮可以接收点击事件 */
		padding: 0 20rpx; /* 添加左右内边距 */
	}

	.btn-wrapper {
		background-color: rgba(0, 0, 0, 0.1); /* 更轻的透明效果，和背景遮罩协调 */
		color: #ffffff;
		display: flex;
		height: 100%;
		width: 100%;
		justify-content: space-around;
		align-items: center;
		border-radius: 15px; /* 稍微增加圆角 */
		margin: 0; /* 移除左右边距，让按钮区域全宽 */
		border: 1px solid rgba(255, 255, 255, 0.15); /* 更轻的边框 */
		backdrop-filter: blur(2px); /* 轻微的背景模糊效果 */
		-webkit-backdrop-filter: blur(2px); /* Safari兼容 */
	}

	.btn-wrapper view {
		width: 200rpx;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		font-size: 18px;
		color: #ffffff;
		z-index: 1000; /* 进一步提高z-index */
		background-color: rgba(255, 255, 255, 0.15); /* 轻微的白色背景 */
		border-radius: 8px;
		border: 1px solid rgba(255, 255, 255, 0.3); /* 轻微的白色边框 */
		font-weight: bold; /* 增加字体粗细 */
		text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6); /* 适中的文字阴影 */
		box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2); /* 轻微的按钮阴影 */
	}

	.hover {
		color: #f1f1f1;
		background-color: rgba(255, 255, 255, 0.25) !important; /* 点击时稍微亮一些 */
		transform: scale(0.98); /* 轻微的缩放效果 */
	}
</style>

<template>
	<view class="content-view" @touchmove.stop.prevent="() => {}">
		<Navbar :hideBtn="false" title="饮食选择" bgColor="#b7bbfa" :h5Show="false" :fixed="true" :titleStyle="{color:'#FFFFFF', fontSize:'16px', fontWeight:'500'}"></Navbar>
		<!-- 顶部提示 -->
		<view class="select-box-head" style="margin-top: 30rpx;">
			<view style="width: 100%; display: flex; align-items: center; justify-content: center;">
				<u-icon name="search" color="#4f41ac" size="28"></u-icon>
				<text style="margin-left: 10rpx; color: #666; font-size: 26rpx;">没找到我要的食物?</text>
				<text style="margin-left: 10rpx; color: #4f41ac; font-weight: bold; font-size: 26rpx;" @click="toBhSearchPage">点我试试看</text>
			</view>
		</view>
		
		<!-- 图片上传区域 -->
		<view class="select-box-head">
			<view @click="fabClick" class="camera-click-area">
				<view style="background: linear-gradient(135deg, #4f41ac, #6a5acd); width: 60rpx; height: 60rpx; border-radius: 50%; display: flex; justify-content: center; align-items: center;">
					<u-icon name="camera" color="#fff" size="28"></u-icon>
				</view>
				<text style="margin-left: 16rpx; color: #333; font-size: 26rpx; font-weight: 500;">拍照记录当餐饮食</text>
			</view>
			<view v-if="curItem.eatImg" style="position: relative;">
				<u--image :src="curItem.eatImg" width="80rpx" height="80rpx" radius="12rpx"></u--image>
				<view style="position: absolute; top: -10rpx; right: -10rpx; background-color: rgba(0,0,0,0.5); width: 30rpx; height: 30rpx; border-radius: 50%; display: flex; justify-content: center; align-items: center;">
					<u-icon name="checkmark" color="#fff" size="16"></u-icon>
				</view>
			</view>
		</view>
		
		<!-- 搜索区域 -->
		<view class="select-box-head">
			<view style="width: 100%;">
				<u-search class="search-box-input" v-model="inputValue" :placeholder="placeholderText"
					bgColor="#f5f7fa" :animation="true" :clearabled="true" :showAction="true" 
					@search="searchChange" @clear="clearClick" @custom="searchChange"></u-search>
			</view>
		</view>
		 
		<!-- 提示信息 -->
		<view style="font-size: 24rpx; text-align: center; color: #999; margin: 16rpx 0; font-style: italic;">『食物成分数据仅作为饮食参考，不代表精准数据』</view>
		
		<!-- 食物分类和列表 -->
		<view class="a-list" v-if="isShowShopp">
			<!-- 左侧分类菜单 -->
			<scroll-view class="menu-list" scroll-y="true">
				<ul class="item-ul" v-for="(item, index) in foodTree" :key="item.id"
					:class="nagTypeIndex == index ? 'nagNormal' : ''" @tap="menuClick(item,index)">
					<li>{{item.foodName}}</li>
				</ul>
			</scroll-view>

			<!-- 右侧食物列表 -->
			<scroll-view class="right-list" scroll-y="true" @scrolltolower="scrolltolower">
				<!-- 空状态 -->
				<view v-if="foodChildList.length < 1">
					<u-empty mode="data" text="未查询到符合条件的数据" margin-top="60"></u-empty>
				</view>
				
				<!-- 食物列表 - 网格布局 -->
				<view class="card-dept-box">
					<view class="card-view" @click="addFood(item)" v-for="(item,index) in foodChildList" :key="item.id">
						<view class="text-vie">
							<view class="ugrid-text">{{item.foodName}}</view>
							<view class="energy-tag">
								<!-- 食物列表中的热量显示 -->
								<view class="energy-badge" :class="{
									'energy-badge-low': getEnergyColor(item.energy, item.weight) === 'green',
									'energy-badge-medium': getEnergyColor(item.energy, item.weight) === 'orange',
									'energy-badge-high': getEnergyColor(item.energy, item.weight) === 'red'
								}">
									{{ parseInt(item.energy) }} kcal
								</view>
								
								<view class="weight-info">{{item.weight}}g</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 底部操作栏 -->
		<view v-if="!popShow" class="bottom-action-bar">
			<view style="width:30%; display: flex; justify-content: center;">
				<view class="food-basket" @click="showFoodItems">
					<u--image src="https://mobile-1323860534.cos.ap-guangzhou.myqcloud.com/fat/eatIcon.png" width="80rpx"
						height="50rpx" @click="showFoodItems">
					</u--image>
					<view style="position: absolute; top: -10rpx; right: -10rpx;">
						<u-badge type="error" v-if="foodItems && foodItems.length>0" max="99"
							:value="foodItems.length"></u-badge>	
					</view>
				</view>
			</view>
			<view style="width: 60%;">
				<button size="large" @click="finishFoodEdit" type="success" style="font-size: 28rpx;">完成选择</button>
			</view>
		</view>

		<!-- 已选食物弹出层 -->
		<u-popup v-if="foodItems && foodItems.length>0" :show="itemsShow" mode="bottom" @close="itemsShow=false"
			:round="14">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">已选食物</view>
					<view class="popup-close" @click="itemsShow=false">
						<u-icon name="close" color="#999" size="24"></u-icon>
					</view>
				</view>
				
				<view style="padding: 10rpx 30rpx; max-height: 60vh; overflow-y: auto; padding-bottom: 150rpx; box-sizing: border-box;">
					<view v-for="i in foodItems" :key="i.id" class="card-view food-item" style="width: calc(100% - 20rpx); display: flex; align-items: center; justify-content: space-between;">
						<view style="flex: 1; overflow: hidden;">
							<view class="ugrid-text" style="margin-bottom: 8rpx;">{{i.foodName}}</view>
							<view class="energy-tag">
								<!-- 已选食物弹出层中的热量显示 -->
								<view class="energy-badge" :class="{
									'energy-badge-low': getEnergyColor(i.energy, i.weight) === 'green',
									'energy-badge-medium': getEnergyColor(i.energy, i.weight) === 'orange',
									'energy-badge-high': getEnergyColor(i.energy, i.weight) === 'red'
								}">
									{{ parseInt(i.energy) }} kcal
								</view>
								<view class="weight-info">{{i.weight}}g</view>
							</view>
						</view>
						<view @click="removeItem(i)" style="padding: 8rpx; flex-shrink: 0;">
							<u-icon name="trash" color="#ff5252" size="24"></u-icon>
						</view>
					</view>
				</view>

			
			</view>
		</u-popup>

		<!-- 食物详情弹出层 -->
		<u-popup :show="popShow" mode="bottom" style="z-index: 10080;" @close="popShow=false" :round="14">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">食物详情</view>
					<view class="popup-close" @click="popShow=false">
						<u-icon name="close" color="#999" size="24"></u-icon>
					</view>
				</view>
				
				<view style="padding: 30rpx;">
					<!-- 食物标题 -->
					<view style="margin-bottom: 30rpx;">
						<view style="font-size: 32rpx; font-weight: bold; color: #333; margin-bottom: 10rpx;">{{showFood.foodName}}</view>
						<view class="font12" style="color: #666;">标准份量: <text style="color: #4f41ac; font-weight: bold;">{{ showFood.weight }}g</text></view>
					</view>

					<!-- 营养成分 -->
					<view class="nutrition-card">
						<view style="font-size: 28rpx; font-weight: bold; color: #333; margin-bottom: 20rpx;">营养成分</view>
						<view class="nutrition-grid">
							<view class="nutrition-item">
								<view class="nutrition-label">能量</view>
								<view class="nutrition-value">{{parseInt(curItem.energy)}} kcal</view>
							</view>
							<view class="nutrition-item">
								<view class="nutrition-label">蛋白质</view>
								<view class="nutrition-value">{{curItem.protein}} g</view>
							</view>
							<view class="nutrition-item">
								<view class="nutrition-label">脂肪</view>
								<view class="nutrition-value">{{curItem.fat}} g</view>
							</view>
							<view class="nutrition-item">
								<view class="nutrition-label">碳水化合物</view>
								<view class="nutrition-value">{{curItem.ch2o}} g</view>
							</view>
						</view>
					</view>
					
					<!-- 重量输入 -->
					<view class="weight-input-container">
						<view style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20rpx;">
							<view style="font-size: 26rpx; font-weight: bold; color: #333;">设置重量</view>
							<view style="display: flex; align-items: center;">
								<image style="width: 36rpx; height: 36rpx; margin-right: 10rpx;"
									src="https://mobile-1323860534.cos.ap-guangzhou.myqcloud.com/fat/weight.png" />
								<text style="color: #4f41ac; font-size: 24rpx;">估算重量</text>
							</view>
						</view>
						
						<view style="font-size: 24rpx; color: #ff5252; margin-bottom: 20rpx;">
							*请输入食物的重量
						</view>
						
						<view class="weight-input-wrapper">
							<input v-if="curItem.weightType=='克'" v-model="curItem.weight" class="weight-input" />
							<input v-if="curItem.weightType=='份'" v-model="curItem.portionNum" class="weight-input" />
							<text style="margin-left: 16rpx; font-size: 28rpx; color: #333;">{{curItem.weightType}}</text>
						</view>
						
						<view class="weight-type-selector">
							<text class="weight-type-label">克</text>
							<u-switch space="2" size="35" active-value="份" inactive-value="克" v-model="curItem.weightType"
								activeColor="#f9ae3d" inactiveColor="#4f41ac">
							</u-switch>
							<text class="weight-type-label">份</text>
						</view>
					</view>

					<!-- 确认按钮 -->
					<button type="success" style="margin-top: 30rpx;" @click="addFoodItem">确定</button>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import * as FoodApi from "@/api/work/food.js"
	import {
		handleTree
	} from '@/utils/ruoyi'
	import * as EatApi from "@/api/work/eat.js"
	import appConfig from "@/common/config";
	import Navbar from '@/components/navbar/Navbar'
	import { recognizeMultipleFoods } from "@/api/food.js"
	export default {
		components: {
			Navbar
		},
		props: {
			nagTypePros: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				addImgShow:true,
				position: {
					top: '500rpx',
					left: '300rpx'
				},
				itemsShow: false,
				curItem: {
					portionNum: 1,
					weightType: '克',
					eatIf: 'Y',
					dataType: "0"
				},
				foodItems: [],
				eatType: null,
				weightType: '克',
				popShow: false,
				foodTree: [],
				foodChildList: [],
				inputValue: '',
				nagTypeIndex: this.nagTypePros,
				shopChildList: [],
				showFood: {},
				isShowShopp: true, //分类
				selValue: 0,
				rangeList: [{
					value: 0,
					text: "类别"
				}, {
					value: 1,
					text: "关键词"
				}],
				placeholderText: '',
				mdTimer: null,
				eat: null,
				foodQueryParams:{
					pageNum: 1,
					pageSize: 20,
					parentId:""
				},
				total:0,
			}
		},
		onLoad(option) {
			this.curItem.eatType = option.eatType;
			this.curItem.eatTime = option.eatTime;
			this.placeholderText = '请输入食物名称';
			//获取食物分类表
			// this.getFoodList()
			this.getFoodCategory()
			//获取用户当前餐别的提交记录
			this.getUserFoodItems();
		},
		onShow() {
		   if(this.inputValue){
			   this.searchChange(this.inputValue)
		   }	
		},
		watch: {
			popShow(newVal){
				if(!newVal){
					this.curItem.energy = 0
					this.curItem.protein = 0
					this.curItem.fat = 0
					this.curItem.ch2o = 0
					this.curItem.weight=0
					this.curItem.portionNum=0
					this.curItem.df = 0
					this.curItem.va = 0
					this.curItem.vc = 0
					this.curItem.ve = 0
					this.curItem.ca = 0
					this.curItem.pp = 0
					this.curItem.kk = 0
					this.curItem.na = 0
					this.curItem.fe = 0
					this.curItem.zn = 0
					this.curItem.ii = 0
					this.curItem.imgUrl=null
					this.curItem.foodImg=null
					this.curItem.foodId=null
					this.curItem.foodName=null
					this.curItem.weightType = '克'
					this.curItem.proteinType = null
					this.curItem.ch2oType = null
				}
			},
			inputValue(newVal, oldVal) {
				if (!newVal) {
					this.isShowShopp = true;
					this.isShowPicture = false;
					this.nagTypeIndex = 0;
					this.foodChildList = this.foodTree[0].children;
				}
			},
			curItem: {
				handler(newVal, oldVal) {
					console.log(newVal)
					if (newVal && newVal.weightType && newVal.eatIf != 'N' && newVal.dataType!=1) {
						let weight = 0;
						if (newVal.weightType == "克") {
							weight = newVal.weight;
						} else {
							weight = newVal.portionNum * this.showFood.weight
						}
						console.log("当前食物重量是：" + weight)
						let a = weight / this.showFood.weight
						console.log("倍数：" + a)
						if(weight!=undefined){
							//能量、蛋白质、脂肪、碳水化合物
							this.curItem.energy = Math.round(a * this.showFood.energy)
							this.curItem.protein = (a * this.showFood.protein).toFixed(1)
							this.curItem.fat = (a * this.showFood.fat).toFixed(1)
							this.curItem.ch2o = (a * this.showFood.ch2o).toFixed(1)
							
							this.curItem.df = (a * this.showFood.df).toFixed(1)
							this.curItem.va = (a * this.showFood.va).toFixed(1)
							this.curItem.vc = (a * this.showFood.vc).toFixed(1)
							this.curItem.ve = (a * this.showFood.ve).toFixed(1)
							this.curItem.ca = (a * this.showFood.ca).toFixed(1)
							this.curItem.pp = (a * this.showFood.pp).toFixed(1)
							this.curItem.kk = (a * this.showFood.kk).toFixed(1)
							this.curItem.na = (a * this.showFood.na).toFixed(1)
							this.curItem.fe = (a * this.showFood.fe).toFixed(1)
							this.curItem.zn = (a * this.showFood.zn).toFixed(1)
							this.curItem.ii = (a * this.showFood.ii).toFixed(1)
						}
					}
				},
				deep: true
			},
		},
		methods: {
			getEnergyColor(energy, weight) {
				if (energy <= 40) {
					return 'green';
				} else if (energy <= 90) {
					return 'orange';
				}else{
					return 'red';
				}
			},
			toBhSearchPage(){
				if(this.inputValue.trim()){
					uni.navigateTo({
						url:'/pageWork/bhSearchPage/bhSearchPage?keyword='+this.inputValue.trim()
					})
				}else{
					uni.$u.toast("请先在输入框搜索要查询的食物名称")
				}
				
			},
			fabClick() {
				this.chooseAndUploadImage()
			},
			async chooseAndUploadImage() {
				if (this.curItem.eatIf == 'N') {
					uni.showToast({
						title: "您已选中此餐未进食",
						icon: 'none'
					})
					return
				}

				let _this = this;
				// 选择图片
				uni.chooseImage({
					count: 1, // 默认9，设置图片的数量
					sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
					success: async (chooseImageRes) => {
						console.log("chooseImageRes", chooseImageRes)
						const tempFilePaths = chooseImageRes.tempFilePaths;

						// 显示加载提示
						uni.showLoading({
							title: 'AI正在识别中...',
							mask: true
						});

						try {
							// 调用AI多食物识别API
							const requestData = {
								filePath: tempFilePaths[0],
								name: 'file'
							}

							const response = await recognizeMultipleFoods(requestData)

							uni.hideLoading();

							if (response.code === 200 && response.data) {
								const result = response.data;

								// 保存图片URL
								_this.curItem.eatImg = tempFilePaths[0]; // 使用本地图片路径显示
								_this.curItem.dataType = "1" // 患者自己上传

								// 如果识别到食物，显示识别结果
								if (result.foods && result.foods.length > 0) {
									_this.showAIRecognitionResult(result);
								} else {
									uni.showToast({
										title: "未识别到食物，请重新拍照",
										icon: 'none'
									})
								}
							} else {
								throw new Error(response.msg || 'AI识别失败')
							}
						} catch (error) {
							uni.hideLoading();
							console.error('AI食物识别失败:', error)
							uni.showToast({
								title: error.message || 'AI识别失败，请重试',
								icon: 'none'
							})
						}
					},
					fail: (chooseImageErr) => {
						console.error('选择图片失败', chooseImageErr);
						uni.showToast({
							title: '选择图片失败',
							icon: 'none'
						})
					}
				});
			},
			// 显示AI识别结果
			showAIRecognitionResult(result) {
				let foodNames = result.foods.map(food => food.foodName).join('、');
				let message = `AI识别到：${foodNames}`;

				uni.showModal({
					title: 'AI识别结果',
					content: message + '\n\n是否直接保存这些食物到当餐记录？',
					confirmText: '保存',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							// 用户选择保存，批量添加识别到的食物
							this.batchAddAIFoods(result.foods);
						} else {
							// 用户取消，只保存图片
							this.addFoodItem();
						}
					}
				});
			},
			// 批量添加AI识别的食物
			async batchAddAIFoods(foods) {
				try {
					uni.showLoading({
						title: '正在保存...'
					});

					// 为每个识别到的食物创建记录
					for (let food of foods) {
						// 创建新的食物项，基于当前curItem
						let foodItem = {
							...this.curItem,
							id: null, // 确保是新记录
							foodName: food.foodName,
							energy: food.calories || 0,
							protein: food.protein || 0,
							fat: food.fat || 0,
							ch2o: food.carbohydrate || 0,
							weight: food.weight || 100, // 默认100g
							weightType: '克',
							dataType: "1", // AI识别
							patientId: this.$store.state.unit.unionId
						};

						// 保存食物项
						await EatApi.saveFoodItem(foodItem);
					}

					uni.hideLoading();
					uni.showToast({
						title: '保存成功'
					});

					// 刷新食物列表
					this.getUserFoodItems();

				} catch (error) {
					uni.hideLoading();
					console.error('批量保存AI食物失败:', error);
					uni.showToast({
						title: '保存失败，请重试',
						icon: 'none'
					});
				}
			},
			finishFoodEdit() {
				if (this.foodItems.length > 0) {
					uni.navigateBack()
				} else {
					EatApi.saveEat(this.curItem).then(res => {
						if (res.code == 200) {
							uni.navigateBack()
						}
					})
				}
			},
			removeItem(item) {
				let _this = this;
				uni.showModal({
					title: "确认要删除吗？",
					success(v1) {
						if (v1.confirm) {
							EatApi.delFoodItem(item.id).then(res => {
								if (res.code == 200) {
									uni.showToast({
										title: "删除成功"
									})
									_this.getUserFoodItems()
								}
							})
						}
					}
				})
			},

			noEat() {
				var _this = this;
				_this.curItem.eatIf = _this.curItem.eatIf == "N" ? 'Y' : 'N'
				if (this.foodItems && this.foodItems.length > 0 && _this.curItem.eatIf == "N") {
					uni.showModal({
						title: "此操作将会清空已选食物,确定操作？",
						success(v1) {
							if (v1.confirm) {
								_this.curItem.eatIf = 'N'
								//删除所有饮食项，将饮食置为未进食
								_this.addFoodItem()
							}
							if (v1.cancel) {
								_this.curItem.eatIf = 'Y'
							}
						}
					})
				}

			},
			showFoodItems() {
				if (this.foodItems && this.foodItems.length > 0) {
					this.itemsShow = true;
				}
			},
			//获取用户当前餐别的提交记录
			getUserFoodItems() {
				EatApi.getUserFoodItems({
					eatType: this.curItem.eatType,eatTime:this.curItem.eatTime
				}).then(res => {
					console.log("获取用户当前餐别的提交记录", res)
					if (res.code == 200) {
						let result = res.data.data;
						
						this.foodItems = result.filter(item=>item.dataType==0)
						
						if(result.length>0){
							let imgItem = result.find(item=>item.dataType==1);
							if(imgItem!=null && imgItem!=undefined){
								this.addImgShow=false;
							}
						}
						
						this.eat = res.data.eat
						if (res.data.eat) {
							this.curItem.eatIf = res.data.eat.eatIf
							// this.curItem.eatImg = res.data.eat.eatImg
						}
					}
				})
			},

			addFoodItem() {
				this.curItem.patientId = this.$store.state.unit.unionId
				// 校验
				let weight = 0;
				if (this.curItem.weightType == "克") {
					weight = this.curItem.weight;
				} else {
					weight = this.curItem.portionNum * this.showFood.weight
				}
				console.log("当前食物重量是：" + weight)
				if (weight <= 0) {
					uni.showToast({
						title: "输入不能为0",
						error: 'error'
					})
				}
				//查看是否已经添加过
				if (this.foodItems && this.foodItems.length > 0) {
					let item = this.foodItems.filter(item => item.foodId == this.showFood.id);
					if (item.length != 0) {
						console.log("已经添加过了", item)
						this.curItem.id = item[0].id
					}
				}
				this.curItem.weight = weight
				EatApi.saveFoodItem(this.curItem).then(res => {
					console.log(res)
					if (res.code == 200) {
						uni.showToast({
							title: "添加成功"
						})
						if (this.curItem.eatIf == 'N') {
							uni.navigateBack()
						} else {
							this.popShow = false;
							this.curItem.id = null;
							this.getUserFoodItems()
						}
					}
				})

			},

			addFood(food) {
				if (this.curItem.eatIf == 'N') {
					uni.showToast({
						title: "您已选中此餐未进食",
						icon: 'none'
					})
				} else {
					this.showFood = food
					this.curItem = {
						...this.curItem,
						...food
					}
					this.curItem.id = null;
					this.curItem.foodId = food.id;
					this.curItem.foodImg = food.imgUrl;
					this.popShow = true
				}
			},
			getFoodCategory(){
			    FoodApi.foodCategory().then(res=>{
					if(res.code==200){
						this.foodTree = res.data
						this.menuClick(this.foodTree[0],0)
					}
				})	
			},
			getFoodList() {
				uni.showLoading({
					title:"加载中..."
				})
				FoodApi.getList().then(res => {
					let result = res.data;
					result.forEach(item => {
						if(item.imgUrl && !item.imgUrl.startsWith("https")){
							item.imgUrl = appConfig.getPicUrl() + item.imgUrl;
						}
					})
					this.foodTree = handleTree(result, "id", "parentId")
					this.foodChildList = this.foodTree[0].children
					
					uni.hideLoading()
				})
			},
			clearClick() {
				this.inputValue = "";
			},
			
			searchChange(value){
				if (!value) return this.$u.toast(this.placeholderText);
				FoodApi.searchByName(value).then(res=>{
					if(res.code==200){
						this.foodChildList = res.data;
						console.log('食物',this.foodChildList)
					}
				})
			},

			searchChange_bak(value) {
				console.log("searchChange",value)
				if (!value) return this.$u.toast(this.placeholderText);
				if (this.selValue === 0) {
					if (this.foodTree.length < 1) return;
					const temp = [];
					if (/^[a-zA-Z]*$/.test(value)) {
						// 如果是拼音
						this.foodTree.map((item, index) => {
							item.children.map((item2) => {
								if (item2.pycode.indexOf(value.toUpperCase()) >= 0) {
									temp.push(item2);
									this.nagTypeIndex = index;
								}
							})
						})
					} else {
						// 不是拼音
						this.foodTree.map((item, index) => {
							item.children.map((item2) => {
								if (item2.foodName.indexOf(value) >= 0) {
									temp.push(item2);
									this.nagTypeIndex = index;
								}
							})
						})
					}
					// 将筛选结果的数据存储
					this.foodChildList = temp;
				} else {
					this.isShowShopp = false;
					this.isShowOther = true;
					this.mdTimer = new Date().getTime();
				}

			},

			menuClick(item, index) {
				if(index!=this.nagTypeIndex){
					this.foodQueryParams.pageNum = 1
					this.foodChildList = []
					this.total = 0
				}
				console.log(item,index)
				this.nagTypeIndex = index;
				// this.foodChildList = item.children;
				this.inputValue = "";
				this.getFoodPage()
			},
			
			scrolltolower(){
				console.log("scrolltolower")
				if(this.foodChildList.length<this.total){
					this.foodQueryParams.pageNum += 1
					this.getFoodPage()
				}else{
					uni.$u.toast("没有更多数据了")
				}
				
			},	
			
			getFoodPage(){
				this.foodQueryParams.parentId = this.foodTree[this.nagTypeIndex].id
				console.log(this.foodQueryParams)
				FoodApi.getFoodPageByParentId(this.foodQueryParams).then(res=>{
					console.log("分布查询数据",res)
					this.total = res.total
					this.foodChildList = [...this.foodChildList,...res.rows]	
				})
			},
			

			changeEvent(e) {
				this.selValue = e;
				this.placeholderText = `请输入食物名称`;
				this.inputValue = "";
				if (this.selValue === 0) {
					this.isShowShopp = true;
					this.isShowPicture = false;
				}
			},
		}
	}
</script>

<style scoped lang="scss">
	@import url('/static/scss/list.scss');
	
	/* 整体容器样式 */
	.content-view {
		background-color: #f8f8f8;
		min-height: 100vh;
		padding-bottom: 140rpx;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
	}
	
	/* 头部样式 */
	.select-box-head {
		display: flex;
		justify-content: space-between;
		padding: 24rpx;
		align-items: center;
		background-color: #fff;
		border-radius: 16rpx;
		margin: 20rpx 24rpx 0;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		transition: all 0.3s ease;
	}

	/* 拍照点击区域样式 */
	.camera-click-area {
		display: flex;
		align-items: center;
		padding: 8rpx;
		border-radius: 12rpx;
		transition: all 0.2s ease;
		min-height: 80rpx;
		flex: 1;
	}

	.camera-click-area:active {
		background-color: rgba(79, 65, 172, 0.05);
		transform: scale(0.98);
	}
	
	/* 搜索框样式 */
	::v-deep .search-box-input {
		border-radius: 40rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	::v-deep .search-box-input .u-search__content {
		background-color: #f5f7fa;
		border-radius: 40rpx;
		padding: 12rpx 24rpx;
	}
	
	::v-deep .search-box-input .u-search__action {
		color: #4f41ac;
		font-weight: 500;
	} 
	
	/* 食物分类列表样式 */
	.a-list {
		display: flex;
		height: calc(100vh - 320rpx);
		margin: 20rpx 24rpx;
		margin-bottom: 50rpx;
		background-color: #fff;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	}
	
	.menu-list {
		width: 180rpx;
		height: 100%;
		background-color: #f5f7fa;
		border-right: 1rpx solid #eee;
		padding: 16rpx 8rpx;
	}
	
	.item-ul {
		padding: 20rpx 0;
		text-align: center;
		font-size: 24rpx;
		color: #666;
		border-left: 6rpx solid transparent;
		transition: all 0.3s ease;
		position: relative;
		margin: 8rpx 10rpx;
		border-radius: 8rpx;
		overflow: hidden;
		background-color: #ffffff;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
	}
	
	.item-ul li {
		padding: 0 6rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		transition: all 0.2s ease;
	}
	 
	.item-ul:not(:last-child)::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 15%;
		width: 70%;
		height: 1rpx;
		background-color: transparent;
	}
	
	.item-ul:hover {
		background-color: rgba(79, 65, 172, 0.05);
		color: #555;
		box-shadow: 0 2rpx 10rpx rgba(79, 65, 172, 0.08);
	}
	
	.nagNormal {
		background: linear-gradient(90deg, rgba(79, 65, 172, 0.15), rgba(79, 65, 172, 0.03));
		color: #4f41ac;
		border-left: 6rpx solid #4f41ac;
		font-weight: bold;
		box-shadow: 0 2rpx 10rpx rgba(79, 65, 172, 0.1);
	}
	
	.nagNormal li {
		color: #4f41ac;
		font-weight: 600;
		transform: scale(1.02);
		transition: all 0.3s ease;
	}
	
	.nagNormal::before {
		content: '';
		position: absolute;
		left: 0;
		top: 0;
		height: 100%;
		width: 6rpx;
		background: linear-gradient(to bottom, #4f41ac, #6a5acd);
		z-index: 1;
	}
	
	/* 右侧食物列表样式 */
	.right-list {
		flex: 1;
		height: 100%;
		background-color: #fff;
		padding: 20rpx;
	}
	
	.card-dept-box {
		padding: 10rpx 0;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		width: 100%;
	}
	
	.food-item {
		width: auto !important;
		padding: 15rpx !important;
		margin: 10rpx 0 !important;
		box-sizing: border-box;
	}

	/* 食物卡片样式 - 创新设计 */
	.card-view {
		width: 100%;
		margin-bottom: 20rpx;
		background-color: #fff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
		transition: all 0.3s ease;
		overflow: hidden;
		position: relative;
		padding: 20rpx;
		border: 1rpx solid rgba(0, 0, 0, 0.03);
	}
	
	.card-view:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
		background-color: rgba(79, 65, 172, 0.02);
	}
	
	.text-vie {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.ugrid-text {
		font-size: 26rpx;
		color: #333;
		font-weight: 600;
		margin-bottom: 0;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		line-height: 1.4;
		max-width: 65%;
	}
	
	/* 热量标签样式 - 创新设计 */
	.energy-tag {
		display: flex;
		align-items: center;
	}
	
	.energy-badge {
		display: inline-flex;
		align-items: center;
		padding: 6rpx 14rpx;
		border-radius: 20rpx;
		font-size: 22rpx;
		font-weight: bold;
		color: #fff;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		margin-right: 10rpx;
	}
	
	.energy-badge-low {
		background: linear-gradient(135deg, #4CAF50, #8BC34A);
	}
	
	.energy-badge-medium {
		background: linear-gradient(135deg, #FF9800, #FFC107);
	}
	
	.energy-badge-high {
		background: linear-gradient(135deg, #F44336, #FF5722);
	}
	
	.weight-info {
		font-size: 22rpx;
		color: #999;
		background-color: #f5f7fa;
		padding: 4rpx 10rpx;
		border-radius: 16rpx;
	}
	
	/* 底部操作栏样式 */
	.bottom-action-bar {
		z-index: 10076;
		display: flex;
		position: fixed;
		bottom: 0;
		left: 0;
		align-items: center;
		justify-content: space-around;
		height: 120rpx;
		width: 100%;
		background-color: #fff;
		box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
		padding-bottom: env(safe-area-inset-bottom);
	}
	
	/* 食物篮子图标 */
	.food-basket {
		position: relative;
		background: linear-gradient(135deg, #f5f7fa, #eef0f5);
		border-radius: 50%;
		width: 80rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		transition: all 0.3s ease;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	}
	
	.food-basket:active {
		transform: scale(0.95);
		background: linear-gradient(135deg, #eef0f5, #e5e8f0);
	}
	
	/* 弹出层样式 */
	.popup-content {
		background-color: #fff;
		border-radius: 24rpx 24rpx 0 0;
		overflow: hidden;
		width: 100%;
		box-sizing: border-box;
	}
	
	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.popup-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
	}
	
	.popup-close {
		padding: 10rpx;
	}
	
	/* 营养卡片样式 */
	.nutrition-card {
		background-color: #f8f8f8;
		border-radius: 16rpx;
		padding: 24rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.nutrition-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20rpx;
	}
	
	.nutrition-item {
		display: flex;
		flex-direction: column;
	}
	
	.nutrition-label {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 8rpx;
	}
	
	.nutrition-value {
		font-size: 26rpx;
		color: #333;
		font-weight: bold;
	}
	
	/* 重量输入样式 */
	.weight-input-container {
		background-color: #f8f8f8;
		border-radius: 16rpx;
		padding: 24rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.weight-input-wrapper {
		display: flex;
		align-items: center;
		background-color: #fff;
		border-radius: 12rpx;
		padding: 16rpx;
		margin-bottom: 20rpx;
		border: 1rpx solid #eee;
	}
	
	.weight-input {
		flex: 1;
		height: 60rpx;
		font-size: 28rpx;
		color: #333;
	}
	
	.weight-type-selector {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 10rpx;
	}
	
	.weight-type-label {
		font-size: 26rpx;
		color: #666;
		margin: 0 16rpx;
	}
	
	/* 按钮样式 */
	button[type="success"] {
		background: linear-gradient(135deg, #4f41ac, #6a5acd);
		border-radius: 12rpx;
		font-size: 32rpx;
		font-weight: 500;
		box-shadow: 0 6rpx 20rpx rgba(79, 65, 172, 0.2);
		transition: all 0.3s ease;
		color: #fff;
	}
	
	button[type="success"]:active {
		transform: translateY(2rpx);
		box-shadow: 0 2rpx 10rpx rgba(79, 65, 172, 0.2);
	}
</style>

package com.ruoyi.web.controller.base;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.web.domain.CaszHosPillHave;
import com.ruoyi.web.domain.CaszPillStandard;
import com.ruoyi.web.service.ICaszHosPillHaveService;
import com.ruoyi.web.service.ICaszPillStandardService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**--
 * 医疗机构药品配备Controller
 * 
 * <AUTHOR>
 * @date 2024-01-24
 */
@RestController
@RequestMapping("/baseCondition/pillHave")
public class CaszHosPillHaveController extends BaseController
{
    @Autowired
    private ICaszHosPillHaveService caszHosPillHaveService;
    @Autowired
    private ICaszPillStandardService caszPillStandardService;

    /**
     * 查询医疗机构药品配备列表
     */
    @PreAuthorize("@ss.hasPermi('baseCondition:pillHave:list')")
    @GetMapping("/list")
    public AjaxResult list(CaszHosPillHave caszHosPillHave)
    {
        if("admin".equals(getUsername())) return null;
        List<CaszPillStandard> caszPillStandards = caszPillStandardService.selectCaszPillStandardList(null);
        List<CaszHosPillHave> list = caszHosPillHaveService.selectByUnitId(getUnitId());
        List<CaszHosPillHave> waitSave = new ArrayList<>();
        List<CaszHosPillHave> waitUpdate = new ArrayList<>();
        for (CaszPillStandard caszPillStandard : caszPillStandards) {
            //查询是否已经存在记录
            LambdaQueryWrapper<CaszHosPillHave> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(CaszHosPillHave::getHosId,getUnitId());
            lambdaQueryWrapper.eq(CaszHosPillHave::getPillStandardId,caszPillStandard.getId());
            CaszHosPillHave one = caszHosPillHaveService.getOne(lambdaQueryWrapper);
            if(one!=null){//动态更新需要具备的状态
                one.setSuggestHave(caszPillStandard.getRequired());
                one.setUpdateTime(new Date());
                waitUpdate.add(one);
            }else{
                CaszHosPillHave pillHave = new CaszHosPillHave();
                pillHave.setAlreadyHave("N");
                pillHave.setHosId(getUnitId());
                pillHave.setPillTypeName(caszPillStandard.getName());
                pillHave.setSuggestHave(caszPillStandard.getRequired());
                pillHave.setPillStandardId(caszPillStandard.getId());
                waitSave.add(pillHave);
            }
        }
        if(waitSave.size()>0)caszHosPillHaveService.saveBatch(waitSave);
        if(waitUpdate.size()>0) caszHosPillHaveService.updateBatchById(waitUpdate);
        caszHosPillHave.setHosId(getUnitId());
        List<CaszHosPillHave> allList = caszHosPillHaveService.selectCaszHosPillHaveList(caszHosPillHave);
        return success(allList);
    }

    /**
     * 导出医疗机构药品配备列表
     */
    @PreAuthorize("@ss.hasPermi('baseCondition:pillHave:export')")
    @Log(title = "医疗机构药品配备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CaszHosPillHave caszHosPillHave)
    {
        List<CaszHosPillHave> list = caszHosPillHaveService.selectCaszHosPillHaveList(caszHosPillHave);
        ExcelUtil<CaszHosPillHave> util = new ExcelUtil<CaszHosPillHave>(CaszHosPillHave.class);
        util.exportExcel(response, list, "医疗机构药品配备数据");
    }

    /**
     * 获取医疗机构药品配备详细信息
     */
    @PreAuthorize("@ss.hasPermi('baseCondition:pillHave:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(caszHosPillHaveService.selectCaszHosPillHaveById(id));
    }

    /**
     * 新增医疗机构药品配备
     */
    @PreAuthorize("@ss.hasPermi('baseCondition:pillHave:add')")
    @Log(title = "医疗机构药品配备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CaszHosPillHave caszHosPillHave)
    {
        return toAjax(caszHosPillHaveService.save(caszHosPillHave));
    }

    /**
     * 修改医疗机构药品配备
     */
    @PreAuthorize("@ss.hasPermi('baseCondition:pillHave:edit')")
    @Log(title = "医疗机构药品配备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CaszHosPillHave caszHosPillHave)
    {
        return toAjax(caszHosPillHaveService.updateCaszHosPillHave(caszHosPillHave));
    }

    /**
     * 删除医疗机构药品配备
     */
    @PreAuthorize("@ss.hasPermi('baseCondition:pillHave:remove')")
    @Log(title = "医疗机构药品配备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(caszHosPillHaveService.deleteCaszHosPillHaveByIds(ids));
    }
}

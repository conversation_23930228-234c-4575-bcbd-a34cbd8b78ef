package com.ruoyi.im.constant;

import org.bouncycastle.pqc.crypto.newhope.NHSecretKeyProcessor;

public class YeImApiConstant {

    public static final Integer TOKEN_EXPIRE = 7200000;

    public static final String DOCTOR_MAN_URL = "https://fat-**********.cos.ap-nanjing.myqcloud.com/10847b9b-fd14-44db-a831-a98121254ee9.png";
    public static final String DOCTOR_WOMAN_URL = "https://fat-**********.cos.ap-nanjing.myqcloud.com/10847b9b-fd14-44db-a831-a98121254ee9.png";
    public static final String UNIT_AI_URL = "https://mobile-**********.cos.ap-guangzhou.myqcloud.com/yeim/Al-Chatbot.png";
    public static final String SECRET = "50abd47112ebe8c5a73f4694c96a49ce";

//    public static final String IM_URL = "http://192.168.110.196:10010/";
    public static final String IM_URL = "http://8.138.195.31:10010/";//test
//    public static final String IM_URL = "http://127.0.0.1:10010/";
//    public static final String IM_URL = "https://yeim.caszmt.com/";//协和
//    public static final String IM_URL = "https://im.caszmt.com/";//泉州

    public static final String REGIST_URL_HTTP = "user/register";
    public static final String CREATE_GROUP_URL_HTTP = "group/create";
    public static final String USER_TOKEN_URL_HTTP = "user/token/get";
    public static final String ADD_FRIEND_URL_HTTP = "friend/add";
    public static final String FRIEND_APPLY_LIST_URL = "/friend/apply/list?type=0&";
    public static final String FRIEND_LIST_URL_HTTP = "friend/list";
    public static final String ACCEPT_FRIEND_APPLY_URL = "/friend/apply/accept";
    public static final String REFUSE_FRIEND_APPLY_URL = "/friend/apply/refuse";
    public static final String CONVERSATION_LIST_URL = "/conversation/list";
    public static final String UPDATE_USER_INFO_URL = "user/update";
    public static final String SEND_MSG_URL = "message/save";
    public static final String JOIN_GROUP_URL = "group/user/add";
    public static final String MESSAGE_HISTORY_URL = "v117/message/list";
    public static final String CLEAR_UNREAD_URL = "conversation/update/unread";
    public static final String GROUP_MEMBERS_URL = "group/user/list";
    public static final String REGIST_URL = IM_URL + REGIST_URL_HTTP;
    public static final String CREATE_GROUP_URL = IM_URL + CREATE_GROUP_URL_HTTP;
    public static final String USER_TOKEN_URL = IM_URL + USER_TOKEN_URL_HTTP;
    public static final String ADD_FRIEND_URL = IM_URL + ADD_FRIEND_URL_HTTP;
    public static final String FRIEND_APPLY_LIST = IM_URL + FRIEND_APPLY_LIST_URL;
    public static final String ACCEPT_FRIEND_APPLY = IM_URL + ACCEPT_FRIEND_APPLY_URL;
    public static final String REFUSE_FRIEND_APPLY = IM_URL + REFUSE_FRIEND_APPLY_URL;
    public static final String UPDATE_USER_INFO = IM_URL + UPDATE_USER_INFO_URL;
    public static final String CONVERSATION_LIST = IM_URL + CONVERSATION_LIST_URL;
    public static final String SEND_MSG = IM_URL + SEND_MSG_URL;
    public static final String JOIN_GROUP = IM_URL + JOIN_GROUP_URL;
    public static final String FRIEND_LIST_URL = IM_URL + FRIEND_LIST_URL_HTTP;
    public static final String MESSAGE_HISTORY = IM_URL + MESSAGE_HISTORY_URL;
    public static final String CLEAR_UNREAD = IM_URL + CLEAR_UNREAD_URL;
    public static final String GROUP_MEMBERS = IM_URL + GROUP_MEMBERS_URL;

}

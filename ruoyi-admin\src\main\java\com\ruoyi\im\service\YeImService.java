package com.ruoyi.im.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.fat.domain.FatImUser;
import com.ruoyi.im.constant.ImApiConstant;
import com.ruoyi.im.constant.YeImApiConstant;
import com.ruoyi.im.domain.Message;
import com.ruoyi.im.domain.YeMessage;
import com.ruoyi.im.utils.YeImUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class YeImService {

    @Autowired
    private RedisCache redisCache;

    public String getUserLoginToken(String userId) {
        String token = null;
        Object adminToken = redisCache.getCacheObject(userId);
        if (adminToken != null) {
            System.out.println("直接从redis中获取用户token");
            return adminToken.toString();
        }
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(YeImApiConstant.USER_TOKEN_URL);
        JSONObject param = new JSONObject();
        long l = System.currentTimeMillis() + YeImApiConstant.TOKEN_EXPIRE;
        String sign = YeImUtils.sign(userId, l);
        param.put("sign", sign);
        param.put("timestamp", l);
        param.put("userId", userId);
        String jsonString = param.toJSONString();
        StringEntity stringEntity = new StringEntity(jsonString, StandardCharsets.UTF_8);
        httpPost.setEntity(stringEntity);
        httpPost.setHeader("operationID", UUID.fastUUID().toString());
        httpPost.setHeader("Content-Type", "application/json;charset=utf8");
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            System.out.println(response.getStatusLine());
            if (entity != null) {
                JSONObject result = JSONObject.parseObject(EntityUtils.toString(entity));
                System.out.println("token result"+result);
                Integer integer = result.getInteger("code");
                if (integer == 200) {
                    JSONObject data = result.getJSONObject("data");
                    token = data.getString("token");
                    redisCache.setCacheObject(userId, token, YeImApiConstant.TOKEN_EXPIRE, TimeUnit.MILLISECONDS);
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (httpClient != null)
                    httpClient.close();
                if (response != null)
                    response.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return token;
    }

    /**
     * 用户注册
     *
     * @param imUser
     * @return
     */
    public JSONObject regist(FatImUser imUser) {
        JSONObject result = new JSONObject();
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(YeImApiConstant.REGIST_URL);
        JSONObject registUser = new JSONObject();
        registUser.put("userId", imUser.getPhoneNumber());
        registUser.put("nickname", imUser.getNickname());

        String faceUrl = imUser.getFaceUrl();
        if(StringUtils.isEmpty(faceUrl)){
            faceUrl = YeImUtils.radomHead();
        }

        registUser.put("avatarUrl", faceUrl);
        String jsonString = registUser.toJSONString();
        StringEntity stringEntity = new StringEntity(jsonString, StandardCharsets.UTF_8);
        httpPost.setEntity(stringEntity);
        // httpPost.setHeader("operationID", UUID.fastUUID().toString());
        httpPost.setHeader("Content-Type", "application/json;charset=utf8");
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            System.out.println(response.getStatusLine());
            if (entity != null) {
                result = JSONObject.parseObject(EntityUtils.toString(entity));
                System.out.println(result);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (httpClient != null)
                    httpClient.close();
                if (response != null)
                    response.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return result;
    }

    /**
     * {
     * "memberUserIDs": [
     * "1225441072"
     * ],
     * "adminUserIDs": [
     * "2065939257"
     * ],
     * "ownerUserID": "1054527962",
     * "groupInfo": {
     * "groupID": "xadxwr24",
     * "groupName": "yourg group name",
     * "notification": "notification",
     * "introduction": "introduction",
     * "faceURL": "faceURL url",
     * "ex": "ex",
     * "groupType": 2,
     * "needVerification": 0,
     * "lookMemberInfo": 0,
     * "applyMemberFriend": 0
     * }
     * }
     *
     * @return
     */
    /**
     * 创建群聊
     *
     * @param memberUserIDs
     * @param adminUserIDs
     * @param ownerUserID
     * @param groupID
     * @param groupName
     * @param needVerification
     * @return
     */
    public String createGroup(List<String> memberUserIDs,
            List<String> adminUserIDs,
            String ownerUserID,
            String groupID,
            String groupName,
            Integer needVerification, String patientImID) {

        Set<String> members = new HashSet<>(memberUserIDs);
        members.add(patientImID);
        members.add(ownerUserID);
        members.addAll(adminUserIDs);

        JSONObject groupInfo = new JSONObject();
        groupInfo.put("groupID", groupID);
        groupInfo.put("name", groupName);
        groupInfo.put("introduction", "本群主要用于健康问题交流，请勿他用，注意言辞！");
        groupInfo.put("avatarUrl", "https://fat-**********.cos.ap-nanjing.myqcloud.com/weixin/chatGroup.png");
        groupInfo.put("members", members);
        /**
         * 0：free、1：check、2：forbid
         */
        groupInfo.put("joinMode", 0);
        String token = getUserLoginToken(ownerUserID);
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(YeImApiConstant.CREATE_GROUP_URL);
        String jsonString = groupInfo.toJSONString();
        StringEntity stringEntity = new StringEntity(jsonString, StandardCharsets.UTF_8);
        httpPost.setEntity(stringEntity);
        httpPost.setHeader("token", token);
        httpPost.setHeader("Content-Type", "application/json;charset=utf8");
        CloseableHttpResponse response = null;
        JSONObject result = null;
        try {
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            System.out.println(response.getStatusLine());
            if (entity != null) {
                result = JSONObject.parseObject(EntityUtils.toString(entity));
                System.out.println(result);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (httpClient != null)
                    httpClient.close();
                if (response != null)
                    response.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        assert result != null;
        return result.toJSONString();
    }

    /**
     *
     * @param fromUserID 该用户主动发起好友申请
     * @param toUserID   该用户收到好友申请
     * @param ex         扩展字段
     * @param reqMsg     申请信息
     * @return
     */
    public Boolean addFriend(String fromUserID, String toUserID, String ex, String reqMsg, Boolean needResponse) {
        Boolean isAddSuccess = false;
        JSONObject params = new JSONObject();
        params.put("userId", toUserID);
        String token = getUserLoginToken(fromUserID);

        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(YeImApiConstant.ADD_FRIEND_URL);
        String jsonString = params.toJSONString();

        StringEntity stringEntity = new StringEntity(jsonString, StandardCharsets.UTF_8);
        httpPost.setEntity(stringEntity);
        httpPost.setHeader("operationID", UUID.fastUUID().toString());
        httpPost.setHeader("Content-Type", "application/json;charset=utf8");
        httpPost.setHeader("token", token);
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                JSONObject result = JSONObject.parseObject(EntityUtils.toString(entity));
                System.out.println("添加好友结果：" + result);
                Integer integer = result.getInteger("code");
                if (integer == 20020) {// 已发送好友申请，请等待对方处理
                    isAddSuccess = true;
                    if (!needResponse) {// 无须审核-直接通过
                        JSONArray friendApplyList = getFriendApplyList(toUserID, 1, 20);
                        Object o1 = friendApplyList.getFirst();
                        if (o1 != null) {
                            JSONObject jsonObject = (JSONObject) o1;
                            JSONObject apply = jsonObject.getJSONObject("apply");
                            JSONArray records = apply.getJSONArray("records");
                            if (!records.isEmpty()) {
                                for (Object record : records) {
                                    JSONObject record1 = (JSONObject) record;
                                    String applyUserId = record1.getString("applyUserId");
                                    String toId = record1.getString("userId");
                                    if (fromUserID.equals(applyUserId) && toUserID.equals(toId)) {
                                        // 通过好友申请
                                        Integer applyId = record1.getInteger("id");
                                        isAddSuccess = dealFriendAdd(applyId, toUserID, true, ex);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (httpClient != null)
                    httpClient.close();
                if (response != null)
                    response.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return isAddSuccess;
    }

    /**
     * 查询我收到的好友申请
     * [
     * {
     * "apply": {
     * "records": [
     * {
     * "id": 2,
     * "applyUserId": "18084787925",
     * "userId": "10000",
     * "status": 1,
     * "createdAt": 1751877746159,
     * "userInfo": {
     * "nickname": "徐凯木",
     * "avatarUrl":
     * "https://fat-**********.cos.ap-nanjing.myqcloud.com/10847b9b-fd14-44db-a831-a98121254ee9.png"
     * }
     * }
     * ],
     * "total": 1,
     * "size": 20,
     * "current": 1,
     * "orders": [],
     * "optimizeCountSql": true,
     * "searchCount": true,
     * "countId": null,
     * "maxLimit": null,
     * "pages": 1
     * },
     * "unread": 1
     * }
     * ]
     *
     * @param toUserID
     * @param pageNum  处理结果，1：同意；-1：拒绝
     * @param pageSize 处理信息
     * @return
     */
    public JSONArray getFriendApplyList(String toUserID, Integer pageNum, Integer pageSize) {
        String token = getUserLoginToken(toUserID);
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpGet httpget = new HttpGet(YeImApiConstant.FRIEND_APPLY_LIST + "page=" + pageNum + "&limit=" + pageSize);
        httpget.setHeader("operationID", UUID.fastUUID().toString());
        httpget.setHeader("Content-Type", "application/json;charset=utf8");
        httpget.setHeader("token", token);
        CloseableHttpResponse response = null;
        JSONArray applyList = null;
        try {
            response = httpClient.execute(httpget);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                JSONObject result = JSONObject.parseObject(EntityUtils.toString(entity));
                System.out.println("处理好友申请结果：" + result);
                Integer integer = result.getInteger("code");
                if (integer == 200) {
                    applyList = result.getJSONArray("data");
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (httpClient != null)
                    httpClient.close();
                if (response != null)
                    response.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return applyList;
    }

    /**
     * 处理好友审核
     *
     * @param applyId   处理的好友申请记录ID
     * @param recUserId 接收申请的用户id
     * @param ifAggree  true false
     * @param remark    备注
     * @return
     */
    public Boolean dealFriendAdd(Integer applyId, String recUserId, Boolean ifAggree, String remark) {
        Boolean isAddSuccess = false;
        String token = getUserLoginToken(recUserId);
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpGet httpGet;
        if (ifAggree) {
            httpGet = new HttpGet(YeImApiConstant.ACCEPT_FRIEND_APPLY + "?id=" + applyId + "&remark=" + remark);
        } else {
            httpGet = new HttpGet(YeImApiConstant.REFUSE_FRIEND_APPLY + "?id=" + applyId);
        }

        httpGet.setHeader("operationID", UUID.fastUUID().toString());
        httpGet.setHeader("Content-Type", "application/json;charset=utf8");
        httpGet.setHeader("token", token);
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                JSONObject result = JSONObject.parseObject(EntityUtils.toString(entity));
                System.out.println("处理好友申请结果：" + result);
                Integer integer = result.getInteger("code");
                if (integer == 200) {
                    isAddSuccess = true;
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (httpClient != null)
                    httpClient.close();
                if (response != null)
                    response.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return isAddSuccess;
    }

    /**
     *
     * @param userID
     * @param nickname
     * @param faceURL
     * @return
     */
    public Boolean update_user_info_ex(String userID,
                                       String nickname,
                                       String faceURL,String mobile) {
        Boolean isAddSuccess = false;
        JSONObject params = new JSONObject();
        if (StringUtils.isNotEmpty(nickname)) {
            params.put("nickname", nickname);
        }
        if (StringUtils.isNotEmpty(faceURL)) {
            params.put("avatarUrl", faceURL);
        }
        if(StringUtils.isNotEmpty(mobile)){
            params.put("mobile", mobile);
        }
        String token = getUserLoginToken(userID);
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(YeImApiConstant.UPDATE_USER_INFO);
        String jsonString = params.toJSONString();
        StringEntity stringEntity = new StringEntity(jsonString, StandardCharsets.UTF_8);
        httpPost.setEntity(stringEntity);
        httpPost.setHeader("operationID", UUID.fastUUID().toString());
        httpPost.setHeader("Content-Type", "application/json;charset=utf8");
        httpPost.setHeader("token", token);
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                JSONObject result = JSONObject.parseObject(EntityUtils.toString(entity));
                System.out.println("修改用户资料结果：" + result);
                Integer integer = result.getInteger("code");
                if (integer == 200) {
                    isAddSuccess = true;
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (httpClient != null)
                    httpClient.close();
                if (response != null)
                    response.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return isAddSuccess;
    }

    // 获取会话列表

    public JSONArray getConversationList(String userID, Integer pageNum, Integer pageSize) {
        String token = getUserLoginToken(userID);
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpGet httpget = new HttpGet(YeImApiConstant.CONVERSATION_LIST + "?page=" + pageNum + "&limit=" + pageSize);
        httpget.setHeader("operationID", UUID.fastUUID().toString());
        httpget.setHeader("Content-Type", "application/json;charset=utf8");
        httpget.setHeader("token", token);
        CloseableHttpResponse response = null;
        JSONArray applyList = null;
        try {
            response = httpClient.execute(httpget);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                JSONObject result = JSONObject.parseObject(EntityUtils.toString(entity));
                System.out.println("查询" + userID + "会话列表结果：" + result);
                Integer integer = result.getInteger("code");
                if (integer == 200) {
                    applyList = result.getJSONArray("data");
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (httpClient != null)
                    httpClient.close();
                if (response != null)
                    response.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return applyList;
    }

    /**
     * {
     * "conversationId": "", //会话ID
     * "conversationType": "", //会话类型：私聊、群聊
     * "from": "", //发送者ID
     * "to": "", //接收者ID，私聊为对方用户ID，群聊为群组ID
     * "type": "", //消息类型
     * "body": {}, //消息体
     * "extra": "", //扩展消息字段
     * "time": 1668420384000 //发送时间
     * }
     *
     * @return
     */
    /**
     * ContentType
     *
     * YeIMUniSDKDefines.MESSAGE_TYPE.TEXT 文本消息
     * YeIMUniSDKDefines.MESSAGE_TYPE.IMAGE 图片消息
     * YeIMUniSDKDefines.MESSAGE_TYPE.AUDIO 音频消息
     * YeIMUniSDKDefines.MESSAGE_TYPE.VIDEO 小视频消息
     * YeIMUniSDKDefines.MESSAGE_TYPE.LOCATION 位置消息
     * YeIMUniSDKDefines.MESSAGE_TYPE.CUSTOM 自定义消息
     *
     * @param sendID
     * @param recvID
     * @param groupID
     * @param content
     * @return
     *         conversationType
     *         会话类型,private：单聊，group：群聊，
     */
    // 发送文本消息
    public JSONObject sendMessage(String sendID,
            String recvID,
            String groupID,
            String content) {

        JSONObject result = null;
        String conversationId;
        String conversationType;
        if (StringUtils.isNotEmpty(groupID)) {// 群聊
            conversationId = groupID;
            conversationType = "group";
        } else {
            conversationId = recvID;
            conversationType = "private";
        }

        JSONObject contentObj = new JSONObject();
        contentObj.put("conversationId", conversationId);
        contentObj.put("conversationType", conversationType);
        contentObj.put("from", sendID);
        contentObj.put("to", conversationId);
        contentObj.put("type", "text");
        JSONObject body = new JSONObject();
        body.put("text", content);
        contentObj.put("body", body);
        contentObj.put("extra", "");
        contentObj.put("time", System.currentTimeMillis());
        String token = getUserLoginToken(sendID);
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(YeImApiConstant.SEND_MSG);
        String jsonString = contentObj.toJSONString();
        StringEntity stringEntity = new StringEntity(jsonString, StandardCharsets.UTF_8);
        httpPost.setEntity(stringEntity);
        httpPost.setHeader("operationID", UUID.fastUUID().toString());
        httpPost.setHeader("Content-Type", "application/json;charset=utf8");
        httpPost.setHeader("token", token);
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                result = JSONObject.parseObject(EntityUtils.toString(entity));
                System.out.println("发送消息结果：" + result);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (httpClient != null)
                    httpClient.close();
                if (response != null)
                    response.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return result;
    }

    /**
     * {
     * "conversationId": "", //会话ID
     * "conversationType": "", //会话类型：私聊、群聊
     * "from": "", //发送者ID
     * "to": "", //接收者ID，私聊为对方用户ID，群聊为群组ID
     * "type": "", //消息类型
     * "body": {}, //消息体
     * "extra": "", //扩展消息字段
     * "time": 1668420384000 //发送时间
     * }
     *
     * @return
     */
    /**
     * ContentType
     *
     * YeIMUniSDKDefines.MESSAGE_TYPE.TEXT 文本消息
     * YeIMUniSDKDefines.MESSAGE_TYPE.IMAGE 图片消息
     * YeIMUniSDKDefines.MESSAGE_TYPE.AUDIO 音频消息
     * YeIMUniSDKDefines.MESSAGE_TYPE.VIDEO 小视频消息
     * YeIMUniSDKDefines.MESSAGE_TYPE.LOCATION 位置消息
     * YeIMUniSDKDefines.MESSAGE_TYPE.CUSTOM 自定义消息
     *
     * @param sendID
     * @param recvID
     * @param groupID
     * @param content
     * @return
     *         conversationType
     *         会话类型,private：单聊，group：群聊，
     */

    /**
     * 发送自己组装的消息，可组装自定义，文本，语音、文件、图片等消息
     *
     * @param message
     * @return
     */
    public JSONObject sendMyMessage(YeMessage message) {
        String jsonString = message.toJson();
        JSONObject result = null;
        String token = getUserLoginToken(message.getFrom());
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(YeImApiConstant.SEND_MSG);
        StringEntity stringEntity = new StringEntity(jsonString, StandardCharsets.UTF_8);
        httpPost.setEntity(stringEntity);
        httpPost.setHeader("operationID", UUID.fastUUID().toString());
        httpPost.setHeader("Content-Type", "application/json;charset=utf8");
        httpPost.setHeader("token", token);
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                result = JSONObject.parseObject(EntityUtils.toString(entity));
                System.out.println("发送消息结果：" + result);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (httpClient != null)
                    httpClient.close();
                if (response != null)
                    response.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return result;
    }

    /**
     * 字段名 选填 类型 说明
     * groupID 必填 string 群ID
     * inviterUserID 必填 string 申请者ID
     *
     * @param groupID
     * @param inviterUserID
     * @return
     */
    public JSONObject joinGroups(String groupID,
            String inviterUserID) {
        JSONObject result = null;
        JSONObject params = new JSONObject();
        params.put("groupId", groupID);
        params.put("members", Collections.singletonList(inviterUserID));

        String token = getUserLoginToken(inviterUserID);
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(YeImApiConstant.JOIN_GROUP);
        String jsonString = params.toJSONString();

        StringEntity stringEntity = new StringEntity(jsonString, StandardCharsets.UTF_8);
        httpPost.setEntity(stringEntity);
        httpPost.setHeader("operationID", UUID.fastUUID().toString());
        httpPost.setHeader("Content-Type", "application/json;charset=utf8");
        httpPost.setHeader("token", token);
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            System.out.println(response.getStatusLine());
            if (entity != null) {
                result = JSONObject.parseObject(EntityUtils.toString(entity));
                System.out.println(result);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (httpClient != null)
                    httpClient.close();
                if (response != null)
                    response.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return result;
    }

    /**
     * 获取聊天历史记录
     *
     * @param conversationId 会话ID
     * @param nextMessageId  下一条消息ID（用于分页）
     * @param userId         用户ID（用于获取token）
     * @return 聊天历史记录
     */
    public JSONArray getChatHistory(String conversationId, String nextMessageId, String userId) {
        String token = getUserLoginToken(userId);
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();

        // 构建请求URL，添加会话ID和分页参数
        String url = YeImApiConstant.MESSAGE_HISTORY + "?conversationId=" + conversationId;
        if (StringUtils.isNotEmpty(nextMessageId)) {
            url += "&nextMessageId=" + nextMessageId;
        }

        HttpGet httpGet = new HttpGet(url);
        httpGet.setHeader("operationID", UUID.fastUUID().toString());
        httpGet.setHeader("Content-Type", "application/json;charset=utf8");
        httpGet.setHeader("token", token);

        CloseableHttpResponse response = null;
        JSONArray chatHistory = new JSONArray();

        try {
            response = httpClient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                JSONObject result = JSONObject.parseObject(EntityUtils.toString(entity));
                System.out.println("获取会话" + conversationId + "历史消息记录结果：" + result);
                Integer code = result.getInteger("code");
                if (code == 200) {
                    JSONObject data = result.getJSONObject("data");
                    if (data != null) {
                        chatHistory = data.getJSONArray("records");
                        if (chatHistory == null) {
                            chatHistory = new JSONArray();
                        }
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            System.err.println("获取聊天历史记录失败: " + e.getMessage());
        } finally {
            try {
                if (httpClient != null)
                    httpClient.close();
                if (response != null)
                    response.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        return chatHistory;
    }

    /**
     * 发送语音消息
     *
     * @param sendID   发送者ID
     * @param recvID   接收者ID
     * @param groupID  群组ID
     * @param audioUrl 语音文件URL
     * @param duration 语音时长（秒）
     * @return 发送结果
     */
    public JSONObject sendVoiceMessage(String sendID, String recvID, String groupID, String audioUrl,
            Integer duration) {
        String conversationId;
        String conversationType;
        if (StringUtils.isNotEmpty(groupID)) {// 群聊
            conversationId = groupID;
            conversationType = "group";
        } else {
            conversationId = recvID;
            conversationType = "private";
        }

        YeMessage message = YeMessage.builder()
                .conversationId(conversationId)
                .conversationType(conversationType)
                .from(sendID)
                .to(conversationId)
                .type("audio")
                .time(System.currentTimeMillis())
                .extra("")
                .build()
                .getMessage();

        // 构建语音消息体
        JSONObject audioBody = new JSONObject();
        audioBody.put("audioUrl", audioUrl);
        if (duration != null) {
            audioBody.put("duration", duration);
        }
        message.setBody(audioBody);

        return sendMyMessage(message);
    }

    /**
     * 发送图片消息
     *
     * @param sendID   发送者ID
     * @param recvID   接收者ID
     * @param groupID  群组ID
     * @param imageUrl 图片URL
     * @param content  图片描述（可选）
     * @return 发送结果
     */
    public JSONObject sendImageMessage(String sendID, String recvID, String groupID, String imageUrl, String content) {
        String conversationId;
        String conversationType;
        if (StringUtils.isNotEmpty(groupID)) {// 群聊
            conversationId = groupID;
            conversationType = "group";
        } else {
            conversationId = recvID;
            conversationType = "private";
        }

        YeMessage message = YeMessage.builder()
                .conversationId(conversationId)
                .conversationType(conversationType)
                .from(sendID)
                .to(conversationId)
                .type("image")
                .time(System.currentTimeMillis())
                .extra("")
                .build()
                .getMessage();

        // 构建图片消息体
        JSONObject imageBody = new JSONObject();
        imageBody.put("url", imageUrl);
        if (StringUtils.isNotEmpty(content)) {
            imageBody.put("content", content);
        }
        message.setBody(imageBody);

        return sendMyMessage(message);
    }

    /**
     * 获取群成员列表
     *
     * @param groupId 群组ID
     * @param userId  用户ID（用于获取token）
     * @return 群成员列表
     */
    public JSONArray getGroupMembers(String groupId, String userId) {
        String token = getUserLoginToken(userId);
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();

        // 构建GET请求URL，添加groupId参数
        String url = YeImApiConstant.GROUP_MEMBERS + "?groupId=" + groupId;

        HttpGet httpGet = new HttpGet(url);
        httpGet.setHeader("operationID", UUID.fastUUID().toString());
        httpGet.setHeader("Content-Type", "application/json;charset=utf8");
        httpGet.setHeader("token", token);

        CloseableHttpResponse response = null;
        JSONArray memberList = null;

        try {
            response = httpClient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String responseBody = EntityUtils.toString(entity);
                System.out.println("群成员列表响应: " + responseBody);

                JSONObject result = JSONObject.parseObject(responseBody);

                // 检查响应状态码
                Integer code = result.getInteger("code");
                if (code != null && code == 200) {
                    memberList = result.getJSONArray("data");
                } else {
                    // 如果是404或其他错误，记录详细信息
                    System.err.println("获取群成员列表失败: HTTP状态=" + response.getStatusLine().getStatusCode() +
                                     ", 响应=" + responseBody);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("获取群成员列表异常: " + e.getMessage());
        } finally {
            try {
                if (response != null) response.close();
                if (httpClient != null) httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return memberList != null ? memberList : new JSONArray();
    }

    /**
     * 获取好友列表
     *
     * @param userId   用户ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param profile  是否包含用户资料，1为包含
     * @return 好友列表数据
     */
    public JSONArray getFriendList(String userId, Integer pageNum, Integer pageSize, Integer profile) {
        String token = getUserLoginToken(userId);
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();

        // 构建请求URL
        String url = YeImApiConstant.FRIEND_LIST_URL + "?page=" + pageNum + "&limit=" + pageSize;
        if (profile != null) {
            url += "&profile=" + profile;
        }

        HttpGet httpGet = new HttpGet(url);
        httpGet.setHeader("operationID", UUID.fastUUID().toString());
        httpGet.setHeader("Content-Type", "application/json;charset=utf8");
        httpGet.setHeader("token", token);

        CloseableHttpResponse response = null;
        JSONArray friendList = null;

        try {
            response = httpClient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                JSONObject result = JSONObject.parseObject(EntityUtils.toString(entity));
                System.out.println("获取用户" + userId + "好友列表结果：" + result);
                Integer code = result.getInteger("code");
                if (code == 200) {
                    JSONObject data = result.getJSONObject("data");
                    if (data != null) {
                        friendList = data.getJSONArray("records");
                    }
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (httpClient != null)
                    httpClient.close();
                if (response != null)
                    response.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return friendList;
    }

    /**
     * 清除会话未读数
     * 
     * @param conversationId 会话ID
     * @param userId         用户ID（用于获取token）
     * @return 清除结果
     */
    public Boolean clearConversationUnread(String conversationId, String userId) {
        String token = getUserLoginToken(userId);
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();

        // 构建请求URL
        String url = YeImApiConstant.CLEAR_UNREAD + "?conversationId=" + conversationId;

        HttpGet httpGet = new HttpGet(url);
        httpGet.setHeader("operationID", UUID.fastUUID().toString());
        httpGet.setHeader("Content-Type", "application/json;charset=utf8");
        httpGet.setHeader("token", token);

        CloseableHttpResponse response = null;
        Boolean isSuccess = false;

        try {
            response = httpClient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                JSONObject result = JSONObject.parseObject(EntityUtils.toString(entity));
                System.out.println("清除会话" + conversationId + "未读数结果：" + result);
                Integer code = result.getInteger("code");
                if (code == 200) {
                    isSuccess = true;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
            System.err.println("清除会话未读数失败: " + e.getMessage());
        } finally {
            try {
                if (httpClient != null)
                    httpClient.close();
                if (response != null)
                    response.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return isSuccess;
    }

}

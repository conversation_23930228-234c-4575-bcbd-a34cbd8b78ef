# 干预方案进度条超时问题修复说明

## 问题描述
干预方案的进度条在超时异常之后顶部的提示消息消失了，但后台其实还在重试中。后台有三次重试机制，前端应该保留提示，让用户知道后台仍在处理。

## 问题分析
1. **前端轮询超时**：Android应用中的进度条轮询设置为150次无进度后停止（约2.5分钟），但后台重试机制可能需要更长时间。
2. **重试状态识别不足**：前端没有正确识别后台的重试状态消息，导致在重试期间仍然计算无进度次数。
3. **后端重试机制**：后端有3次重试机制，使用指数退避策略（2秒、4秒、8秒），并在重试时设置"分析失败，准备重试中"状态。

## 修复方案

### 1. 前端修改（Android应用）
**文件：** `app/src/main/java/com/k2fsa/sherpa/onnx/screens/Home.kt`

#### 1.1 延长轮询超时时间
- 将轮询次数从150次增加到300次（约5分钟）
- 给重试机制更多时间完成

#### 1.2 智能重试状态识别
- 检测包含"重试"、"准备重试"、"分析失败"关键词的消息
- 在重试状态下重置无进度计数，给重试更多时间
- 在重试状态下减缓无进度计数增长（每3秒才增加一次）

#### 1.3 改进超时提示消息
- 将超时提示从"分析超时，请稍后重试"改为"分析超时，后台仍在重试中，请稍后查看结果"
- 让用户知道后台仍在处理

#### 修改的功能模块：
- 干预方案进度条轮询
- 病情分析进度条轮询  
- 对话管理进度条轮询

### 2. 后端修改（Java后端）
**文件：** `ruoyi-admin/src/main/java/com/ruoyi/manager/factory/AIAsyncFactory.java`

#### 2.1 为病情分析添加重试机制
原来的`analysis`和`analysisSplit`方法没有重试机制，现在添加了：
- 3次重试机制，使用指数退避策略
- 在重试时设置"分析失败，准备重试中"状态
- 统一的异常处理和重试逻辑

#### 2.2 新增的方法：
- `executeAnalysisWithRetry()` - 带重试的analysis执行方法
- `handleAnalysisRetry()` - analysis重试处理逻辑
- `executeAnalysisSplitWithRetry()` - 带重试的analysisSplit执行方法
- `handleAnalysisSplitRetry()` - analysisSplit重试处理逻辑

## 技术细节

### 前端轮询逻辑改进
```kotlin
// 检测重试状态
val isRetryMessage = messageFromServer.contains("重试") || 
                   messageFromServer.contains("准备重试") ||
                   messageFromServer.contains("分析失败")

if (isRetryMessage) {
    // 重试状态，重置无进度计数
    noProgressCount = 0
    println("检测到重试状态，重置无进度计数: $messageFromServer")
}
```

### 后端重试机制
```java
// 指数退避策略
long delayMs = 2000L * (1L << nextAttempt); // 2秒、4秒、8秒

// 设置重试等待进度
redisCache.setCacheObject(progressKey, "分析失败，准备重试中", 300, TimeUnit.MINUTES);
```

## 预期效果
1. **用户体验改善**：用户在进度条超时后仍能看到后台在重试的提示
2. **更准确的状态反馈**：前端能正确识别重试状态，不会过早移除进度条
3. **统一的重试机制**：所有AI分析功能都有一致的重试逻辑
4. **更长的容错时间**：给复杂的AI分析任务更多完成时间

## 测试建议
1. 测试网络不稳定情况下的重试机制
2. 验证重试状态消息的正确显示
3. 确认超时后的提示消息更新
4. 测试重试成功后的状态更新

## 注意事项
- 修改后的轮询时间更长，需要确保不会影响应用性能
- 重试机制会增加服务器负载，需要监控资源使用情况
- 建议在生产环境部署前进行充分测试
